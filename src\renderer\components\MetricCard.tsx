import React from 'react';
import {
  Paper,
  Typography,
  useTheme,
  alpha,
} from '@mui/material';

interface MetricCardProps {
  value: string | number;
  label: string;
  color?: 'primary' | 'success' | 'warning' | 'error' | 'info';
  sx?: object;
}

/**
 * Reusable metric display card component
 */
const MetricCard: React.FC<MetricCardProps> = ({
  value,
  label,
  color = 'primary',
  sx = {},
}) => {
  const theme = useTheme();

  const getColorConfig = () => {
    switch (color) {
      case 'success':
        return {
          main: theme.palette.success.main || '#4caf50',
          colorKey: 'success.main',
        };
      case 'warning':
        return {
          main: theme.palette.warning.main || '#ff9800',
          colorKey: 'warning.main',
        };
      case 'error':
        return {
          main: theme.palette.error.main || '#f44336',
          colorKey: 'error.main',
        };
      case 'info':
        return {
          main: theme.palette.info.main || '#2196f3',
          colorKey: 'info.main',
        };
      case 'primary':
      default:
        return {
          main: theme.palette.primary.main || '#1976d2',
          colorKey: 'primary',
        };
    }
  };

  const colorConfig = getColorConfig();

  return (
    <Paper
      sx={{
        p: 3,
        textAlign: 'center',
        borderRadius: 2,
        backgroundColor: alpha(colorConfig.main, 0.04),
        border: `1px solid ${alpha(colorConfig.main, 0.2)}`,
        transition: 'all 0.2s ease-in-out',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: theme.shadows[4],
        },
        ...sx,
      }}
    >
      <Typography
        variant="h4"
        color={colorConfig.colorKey}
        sx={{ fontWeight: 600, mb: 1 }}
      >
        {value}
      </Typography>
      <Typography variant="body2" color="text.secondary">
        {label}
      </Typography>
    </Paper>
  );
};

export default MetricCard;
