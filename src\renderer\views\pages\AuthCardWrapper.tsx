import Box from '@mui/material/Box';
import MainCard from '../../components/cards/MainCard';

// ==============================|| AUTHENTICATION CARD WRAPPER ||============================== //

function AuthCardWrapper({ children, ...other }: any) {
  return (
    <MainCard
      sx={{
        maxWidth: { xs: 400, lg: 475 },
        margin: { xs: 2.5, md: 3 },
        '& > *': {
          flexGrow: 1,
          flexBasis: '50%',
        },
      }}
      {...other}
    >
      <Box sx={{ p: { xs: 2, sm: 3, xl: 5 } }}>{children}</Box>
    </MainCard>
  );
}

export default AuthCardWrapper;
