import {
  IconKey,
  IconSend,
  IconMessage,
  IconUserPlus,
  IconLockOpen,
  IconUsersGroup,
  IconActivity,
  IconAdCircle,
} from '@tabler/icons-react';

const icons = {
  IconKey,
  IconSend,
  IconMessage,
  IconUserPlus,
  IconLockOpen,
  IconUsersGroup,
  IconActivity,
  IconAdCircle,
};

// ==============================|| EXTRA PAGES MENU ITEMS ||============================== //

const pages = {
  id: 'pages',
  // title: 'Pages',
  // caption: 'Pages Caption',
  type: 'group',
  children: [
    {
      id: 'default',
      title: 'Tài khoản',
      type: 'item',
      url: '/dashboard',
      icon: icons.IconUsersGroup,
      breadcrumbs: false,
    },
    {
      id: 'campaign',
      title: '<PERSON>ến dịch',
      type: 'collapse',
      icon: icons.IconAdCircle,
      url: '/campaign/all',
      children: [
        {
          id: 'post',
          title: 'Đăng bài',
          type: 'item',
          url: '/campaign/post',
          icon: icons.IconSend,
        },
        // {
        //   id: 'comment',
        //   title: 'Bình luận',
        //   type: 'item',
        //   url: '/campaign/comment',
        //   icon: icons.IconMessage,
        // },
        // {
        //   id: 'message',
        //   title: 'Nhắn tin',
        //   type: 'item',
        //   url: '/campaign/message',
        //   icon: icons.IconMessage,
        // },
        // {
        //   id: 'add-friend',
        //   title: 'Kết bạn',
        //   type: 'item',
        //   url: '/campaign/add-friend',
        //   icon: icons.IconUserPlus,
        // },
        {
          id: 'scan-group',
          title: 'Quét nhóm',
          type: 'item',
          url: '/campaign/scan-group',
          icon: icons.IconUsersGroup,
        },
        {
          id: 'scan-interaction',
          title: 'Quét tương tác',
          type: 'item',
          url: '/campaign/scan-interaction',
          icon: icons.IconActivity,
        },
      ],
    },
    {
      id: 'authentication',
      title: 'Authentication',
      type: 'collapse',
      icon: icons.IconKey,

      children: [
        {
          id: 'login3',
          title: 'Login',
          type: 'item',
          url: '/pages/login/login3',
          // target: true
        },
        {
          id: 'register3',
          title: 'Register',
          type: 'item',
          url: '/pages/register/register3',
          // target: true
        },
      ],
    },
  ],
};

export default pages;
