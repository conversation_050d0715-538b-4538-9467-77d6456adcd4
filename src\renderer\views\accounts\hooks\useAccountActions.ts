import { useState, useCallback } from 'react';
import { IFBUserResponse } from '../../../../interfaces/IFacebookUser';
import { useAlert } from '../../../hooks/AlertContext';
import { useModal } from '../../../hooks/ConfirmModalContext';
import { useAccountContext } from '../context';
import { messages } from '../../../../utils/messages';

interface UseAccountActionsReturn {
  selectedAccount: IFBUserResponse | undefined;
  passwordVisibility: Record<string, boolean>;
  open: boolean;
  setSelectedAccount: (account: IFBUserResponse | undefined) => void;
  setOpen: (open: boolean) => void;
  togglePasswordVisibility: (accountId: string) => void;
  handleClose: () => void;
  handleDelete: (id: string) => Promise<void>;
  onDelete: (account: IFBUserResponse) => void;
  handleAccount: (account?: IFBUserResponse) => Promise<void>;
  handleReLogin: (account: IFBUserResponse) => Promise<void>;
  openBrowser: (account: IFBUserResponse) => Promise<void>;
  handleBulkDelete: (selectedIds: string[]) => Promise<boolean>;
}

/**
 * Custom hook for account actions and state management
 */
export const useAccountActions = (): UseAccountActionsReturn => {
  const { showAlert } = useAlert();
  const { openConfirmModal } = useModal();
  const { deleteAccount, updateAccount } = useAccountContext();

  const [selectedAccount, setSelectedAccount] = useState<
    IFBUserResponse | undefined
  >(undefined);
  const [passwordVisibility, setPasswordVisibility] = useState<
    Record<string, boolean>
  >({});
  const [open, setOpen] = useState(false);

  const handleClose = useCallback(() => setOpen(false), []);

  const togglePasswordVisibility = useCallback((accountId: string) => {
    setPasswordVisibility((prev) => ({
      ...prev,
      [accountId]: !prev[accountId],
    }));
  }, []);

  const handleDelete = useCallback(
    async (id: string) => {
      try {
        const result = await window.account.deleteUser(id);
        if (result.success) {
          showAlert(messages.general.success, 'success');
          deleteAccount(id);
        } else {
          showAlert(messages.general.error, 'error');
        }
      } catch (error) {
        showAlert(messages.general.error, 'error');
      }
    },
    [showAlert, deleteAccount],
  );

  const onDelete = useCallback(
    (account: IFBUserResponse) => {
      const { id, username } = account;
      openConfirmModal({
        title: 'Xác nhận xóa',
        content: `Bạn có chắc muốn xóa tài khoản "${username}" không?`,
        onSubmit: () => handleDelete(id),
        actions: { cancel: 'Hủy', submit: 'Xóa' },
      });
    },
    [openConfirmModal, handleDelete],
  );

  const fetchUser = useCallback(async (id: string | number) => {
    const response = await window.account.getUser(id);
    if (response && response.data) {
      return response.data;
    }
    return null;
  }, []);

  const handleAccount = useCallback(
    async (account?: IFBUserResponse) => {
      if (account && account.id) {
        const user = await fetchUser(account.id);
        if (user) {
          setSelectedAccount(user); // edit
        }
      } else {
        setSelectedAccount(account); // add new
      }
      setOpen(true);
    },
    [fetchUser],
  );

  const handleReLogin = useCallback(
    async (account: IFBUserResponse) => {
      const { username, password, profileId, categoryId } = account;
      try {
        const loginfb = await window.facebookWeb.login({
          username,
          password,
          profileId,
        });

        if (loginfb && loginfb.success && loginfb.userId) {
          const updatedUser = {
            id: account.id,
            username,
            password,
            profileId,
            userId: loginfb.userId,
            status: 'active',
            categoryId,
          };

          const response = await window.account.updateUser(updatedUser);
          // return if userId existed
          if (response.error) {
            showAlert(response.error, 'warning');
            return;
          }

          // updated success
          if (response.data) {
            updateAccount(account.id, response.data);
            showAlert(messages.account.loginSuccess, 'success');
          }
        }
      } catch (error) {
        showAlert(messages.general.error, 'error');
      }
    },
    [showAlert, updateAccount],
  );

  const openBrowser = useCallback(
    async (account: IFBUserResponse) => {
      if (account.status === 'active') {
        const response = await window.account.launchProfilePage(account.id);
        if (!response.page) {
          showAlert(messages.general.error, 'error');
        }
      } else {
        handleReLogin(account);
      }
    },
    [showAlert, handleReLogin],
  );

  const handleBulkDelete = useCallback(
    async (selectedIds: string[]): Promise<boolean> => {
      if (selectedIds.length === 0) return false;

      try {
        console.log('Bulk deleting accounts:', selectedIds);
        const result = await window.account.bulkDeleteUsers(selectedIds);

        if (result) {
          console.log('API bulk delete successful, updating local state...');

          // Update local state for each deleted account
          let successCount = 0;
          selectedIds.forEach((id) => {
            try {
              console.log(`Removing account ${id} from local state...`);
              deleteAccount(id);
              successCount++;
              console.log(
                `Successfully removed account ${id} from local state`,
              );
            } catch (error) {
              console.error(
                `Failed to remove account ${id} from local state:`,
                error,
              );
            }
          });

          console.log(
            `Local state update complete: ${successCount}/${selectedIds.length} accounts removed`,
          );

          if (successCount === selectedIds.length) {
            showAlert(
              `Đã xóa ${selectedIds.length} tài khoản thành công`,
              'success',
            );
            console.log('All accounts successfully removed from local state');
            return true;
          } else {
            showAlert(
              `Đã xóa ${successCount}/${selectedIds.length} tài khoản. Một số tài khoản có thể cần làm mới trang.`,
              'warning',
            );
            return false;
          }
        } else {
          showAlert('Không thể xóa tài khoản. Vui lòng thử lại.', 'error');
          return false;
        }
      } catch (error) {
        console.error('Error during bulk delete:', error);
        showAlert(
          'Có lỗi xảy ra khi xóa tài khoản. Vui lòng thử lại.',
          'error',
        );
        return false;
      }
    },
    [showAlert, deleteAccount],
  );

  return {
    selectedAccount,
    passwordVisibility,
    open,
    setSelectedAccount,
    setOpen,
    togglePasswordVisibility,
    handleClose,
    handleDelete,
    onDelete,
    handleAccount,
    handleReLogin,
    openBrowser,
    handleBulkDelete,
  };
};
