import React from 'react';
import {
  Paper,
  Stack,
  TextField,
  InputAdornment,
  IconButton,
  Tooltip,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';

interface SearchAndFilterProps {
  searchQuery: string;
  onSearchChange: (value: string) => void;
  placeholder?: string;
  showFilter?: boolean;
  onFilterClick?: () => void;
}

/**
 * Reusable search and filter component
 */
const SearchAndFilter: React.FC<SearchAndFilterProps> = ({
  searchQuery,
  onSearchChange,
  placeholder = 'Tìm kiếm...',
  showFilter = true,
  onFilterClick,
}) => {
  return (
    <Paper elevation={1} sx={{ p: 2, mb: 3, borderRadius: 2 }}>
      <Stack direction="row" spacing={2} alignItems="center">
        <TextField
          placeholder={placeholder}
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
          slotProps={{
            input: {
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon color="action" />
                </InputAdornment>
              ),
            },
          }}
          sx={{
            flexGrow: 1,
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
            },
          }}
        />
        {showFilter && (
          <Tooltip title="Bộ lọc">
            <IconButton color="primary" onClick={onFilterClick}>
              <FilterListIcon />
            </IconButton>
          </Tooltip>
        )}
      </Stack>
    </Paper>
  );
};

export default SearchAndFilter;
