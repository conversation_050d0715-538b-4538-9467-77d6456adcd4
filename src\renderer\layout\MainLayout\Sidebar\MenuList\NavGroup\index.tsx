import { useTheme } from '@mui/material/styles';
import Divider from '@mui/material/Divider';
import List from '@mui/material/List';
import Typography from '@mui/material/Typography';

import NavItem from '../NavItem';
import NavCollapse from '../NavCollapse';

// ==============================|| SIDEBAR MENU LIST GROUP ||============================== //

interface NavGroupProps {
  item: {
    id: string;
    title?: string;
    caption?: string;
    children?: Array<{
      id: string;
      type: 'collapse' | 'item';
      [key: string]: any;
    }>;
  };
  isMiniMode?: boolean;
}

function NavGroup({ item, isMiniMode = false }: NavGroupProps) {
  const theme = useTheme();

  // menu list collapse & items
  const items = item.children?.map((menu) => {
    switch (menu.type) {
      case 'collapse':
        return (
          <NavCollapse
            key={menu.id}
            menu={{
              title: menu.title ?? 'No Title',
              ...menu,
            }}
            level={1}
            isMiniMode={isMiniMode}
          />
        );
      case 'item':
        return (
          <NavItem
            key={menu.id}
            item={{
              title: menu.title ?? 'No Title',
              ...menu,
            }}
            level={1}
            isMiniMode={isMiniMode}
          />
        );
      default:
        return (
          <Typography key={menu.id} variant="h6" color="error" align="center">
            Menu Items Error
          </Typography>
        );
    }
  });

  return (
    <>
      <List
        subheader={
          !isMiniMode &&
          item.title && (
            <Typography
              variant="caption"
              sx={{ ...theme.typography.menuCaption }}
              display="block"
              gutterBottom
            >
              {item.title}
              {item.caption && (
                <Typography
                  variant="caption"
                  sx={{ ...theme.typography.subMenuCaption }}
                  display="block"
                  gutterBottom
                >
                  {item.caption}
                </Typography>
              )}
            </Typography>
          )
        }
        sx={{ p: 0 }}
      >
        {items}
      </List>
    </>
  );
}

export default NavGroup;
