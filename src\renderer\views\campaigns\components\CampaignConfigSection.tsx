import React from 'react';
import {
  Grid,
  Typography,
  Divider,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Paper,
  Alert,
} from '@mui/material';
import SettingsIcon from '@mui/icons-material/Settings';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import { CampaignConfiguration, Campaign } from '../../../../interfaces/Campaigns';
import DetailInfoCard from '../../../components/DetailInfoCard';
import MetricCard from '../../../components/MetricCard';

interface CampaignConfigSectionProps {
  configuration: CampaignConfiguration | null;
  campaign: Campaign;
}

/**
 * Campaign configuration display section
 */
const CampaignConfigSection: React.FC<CampaignConfigSectionProps> = ({
  configuration,
  campaign,
}) => {
  if (!configuration) {
    return (
      <DetailInfoCard
        title="<PERSON><PERSON><PERSON> hình chiến dịch"
        icon={<SettingsIcon />}
        headerColor="#9c27b0"
        iconColor="#9c27b0"
      >
        <Alert severity="info">
          <PERSON>h<PERSON>ng có thông tin cấu hình cho chiến dịch này.
        </Alert>
      </DetailInfoCard>
    );
  }

  const configOptions = [
    {
      key: 'is_anonymous',
      label: 'Ẩn danh',
      value: configuration.is_anonymous,
    },
    {
      key: 'is_joingroup',
      label: 'Tham gia nhóm',
      value: configuration.is_joingroup,
    },
    {
      key: 'tag_friend',
      label: 'Gắn thẻ bạn bè',
      value: configuration.tag_friend,
    },
    {
      key: 'comment_after_post',
      label: 'Bình luận sau đăng',
      value: configuration.comment_after_post,
    },
  ];

  return (
    <DetailInfoCard
      title="Cấu hình chiến dịch"
      icon={<SettingsIcon />}
      headerColor="#9c27b0"
      iconColor="#9c27b0"
    >
      <Grid container spacing={3}>
        {/* Metrics */}
        <Grid size={{ xs: 12, md: 6 }}>
          <MetricCard
            value={configuration.delay ?? 5}
            label="Delay tối đa (giây)"
            color="primary"
          />
        </Grid>
        <Grid size={{ xs: 12, md: 6 }}>
          <MetricCard
            value={configuration.max_post ?? 1}
            label="Số bài tối đa"
            color="success"
          />
        </Grid>

        {/* Configuration Options */}
        <Grid size={12}>
          <Divider sx={{ my: 2 }} />
          <Typography variant="h6" gutterBottom>
            Tùy chọn khác
          </Typography>
          <Grid container spacing={2}>
            <Grid size={{ xs: 12, sm: 6 }}>
              <List dense>
                {configOptions.slice(0, 2).map((option) => (
                  <ListItem key={option.key}>
                    <ListItemAvatar>
                      <Avatar
                        sx={{
                          bgcolor: option.value ? 'success.main' : 'grey.300',
                        }}
                      >
                        {option.value ? (
                          <CheckCircleIcon />
                        ) : (
                          <CancelIcon />
                        )}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={option.label}
                      secondary={option.value ? 'Đã bật' : 'Đã tắt'}
                    />
                  </ListItem>
                ))}
              </List>
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
              <List dense>
                {configOptions.slice(2).map((option) => (
                  <ListItem key={option.key}>
                    <ListItemAvatar>
                      <Avatar
                        sx={{
                          bgcolor: option.value ? 'success.main' : 'grey.300',
                        }}
                      >
                        {option.value ? (
                          <CheckCircleIcon />
                        ) : (
                          <CancelIcon />
                        )}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={option.label}
                      secondary={option.value ? 'Đã bật' : 'Đã tắt'}
                    />
                  </ListItem>
                ))}
              </List>
            </Grid>
          </Grid>
        </Grid>

        {/* Message Content */}
        {campaign.message && (
          <Grid size={12}>
            <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
              <Typography variant="subtitle2" gutterBottom>
                Nội dung tin nhắn:
              </Typography>
              <Typography variant="body2">
                {campaign.message}
              </Typography>
            </Paper>
          </Grid>
        )}

        {/* Comment Content */}
        {configuration.comment_content && (
          <Grid size={12}>
            <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
              <Typography variant="subtitle2" gutterBottom>
                Nội dung bình luận:
              </Typography>
              <Typography variant="body2">
                {configuration.comment_content}
              </Typography>
            </Paper>
          </Grid>
        )}
      </Grid>
    </DetailInfoCard>
  );
};

export default CampaignConfigSection;
