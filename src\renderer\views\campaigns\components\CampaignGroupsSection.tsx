import React, { useState } from 'react';
import {
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  TableContainer,
  Typography,
  Tooltip,
  Alert,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Box,
  Chip,
  Stack,
  useTheme,
  alpha,
  Button,
} from '@mui/material';
import GroupIcon from '@mui/icons-material/Group';
import PersonIcon from '@mui/icons-material/Person';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import CloseIcon from '@mui/icons-material/Close';
import { Group, UserGroup } from '../../../../interfaces/Campaigns';
import DetailInfoCard from '../../../components/DetailInfoCard';
import HourglassEmptyIcon from '@mui/icons-material/HourglassEmpty';
import StatusChip from '../../../components/StatusChip';

interface CampaignGroupsSectionProps {
  groups: Group[] | null;
}

/**
 * Get user posting status configuration
 */
const getUserPostingStatusConfig = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'done':
    case 'public':
      return {
        color: 'success' as const,
        icon: <CheckCircleIcon sx={{ fontSize: 16 }} />,
        label: 'Thành công',
      };
    case 'false':
      return {
        color: 'error' as const,
        icon: <ErrorIcon sx={{ fontSize: 16 }} />,
        label: 'Thất bại',
      };
    case 'pending':
      return {
        color: 'warning' as const,
        icon: <HourglassEmptyIcon sx={{ fontSize: 16 }} />,
        label: 'Đang chờ',
      };
    case 'new':
      return {
        color: 'primary' as const,
        icon: <HourglassEmptyIcon sx={{ fontSize: 16 }} />,
        label: 'Mới',
      };
    default:
      return {
        color: 'default' as const,
        icon: <PersonIcon sx={{ fontSize: 16 }} />,
        label: 'Không xác định',
      };
  }
};

/**
 * User posting status chip component
 */
const UserPostingStatusChip: React.FC<{ status: string }> = ({ status }) => {
  const config = getUserPostingStatusConfig(status);

  return (
    <Chip
      icon={config.icon}
      label={config.label}
      size="small"
      color={config.color}
      variant="outlined"
      sx={{ fontSize: '0.75rem', height: 24 }}
    />
  );
};

/**
 * User group row component for dialog display
 */
const UserGroupRow: React.FC<{ userGroup: UserGroup }> = ({ userGroup }) => {
  const theme = useTheme();

  return (
    <Box
      sx={{
        p: 2,
        backgroundColor: alpha(theme.palette.grey[50] || '#fafafa', 0.5),
        borderRadius: 1,
        border: `1px solid ${alpha(theme.palette.grey[300] || '#e0e0e0', 0.3)}`,
        mb: 1,
      }}
    >
      <Stack direction="row" spacing={2} alignItems="center">
        <Box sx={{ flex: 1 }}>
          <Stack direction="row" spacing={1} alignItems="center">
            <PersonIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              {userGroup.userpost || 'Unknown User'}
            </Typography>
          </Stack>
          {userGroup.profileId && (
            <Typography variant="caption" color="text.secondary">
              Profile ID: {userGroup.profileId}
            </Typography>
          )}
        </Box>
        <UserPostingStatusChip status={userGroup.status} />
      </Stack>
    </Box>
  );
};

/**
 * Campaign groups display section
 */
const CampaignGroupsSection: React.FC<CampaignGroupsSectionProps> = ({
  groups,
}) => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null);

  console.log('groups', groups);

  const handleOpenDialog = (group: Group) => {
    setSelectedGroup(group);
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setSelectedGroup(null);
  };

  const getUserPostingSummary = (userGroups: UserGroup[] | undefined) => {
    if (!userGroups || userGroups.length === 0) {
      return { total: 0, success: 0, failed: 0 };
    }

    const total = userGroups.length;
    const success = userGroups.filter(
      (ug) => ug.status?.toLowerCase() === 'done',
    ).length;
    const failed = userGroups.filter(
      (ug) => ug.status?.toLowerCase() === 'false',
    ).length;

    return { total, success, failed };
  };
  return (
    <DetailInfoCard
      title={`Danh sách nhóm (${groups?.length || 0})`}
      icon={<GroupIcon />}
      headerColor="#ff9800"
      iconColor="#ff9800"
    >
      {groups && groups.length > 0 ? (
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>
                  <strong>Group ID</strong>
                </TableCell>
                <TableCell>
                  <strong>Post ID</strong>
                </TableCell>
                <TableCell>
                  <strong>Trạng thái</strong>
                </TableCell>
                <TableCell>
                  <strong>Người đăng</strong>
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {groups.map((group, index) => {
                const groupKey = group.id || `${group.groupID}-${index}`;
                const summary = getUserPostingSummary(group.UserGroup);

                return (
                  <TableRow
                    key={groupKey}
                    sx={{ '&:hover': { bgcolor: 'grey.50' } }}
                  >
                    <TableCell>
                      <Tooltip title={group.groupID}>
                        <Typography
                          variant="body2"
                          noWrap
                          sx={{ maxWidth: 150 }}
                        >
                          {group.groupID}
                        </Typography>
                      </Tooltip>
                    </TableCell>
                    <TableCell>
                      {group.postId ? (
                        <Tooltip title={group.postId}>
                          <Typography
                            variant="body2"
                            noWrap
                            sx={{ maxWidth: 150 }}
                          >
                            {group.postId}
                          </Typography>
                        </Tooltip>
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          Đăng thất bại/Lỗi
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <UserPostingStatusChip status={group.status} />
                    </TableCell>
                    <TableCell>
                      {summary.total > 0 ? (
                        <Button
                          onClick={() => handleOpenDialog(group)}
                          variant="outlined"
                          size="small"
                          startIcon={<PersonIcon />}
                        >
                          {summary.total} người đăng
                        </Button>
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          Chưa có dữ liệu
                        </Typography>
                      )}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
      ) : (
        <Alert severity="info" icon={<GroupIcon />}>
          Không có nhóm nào được thêm vào chiến dịch này.
        </Alert>
      )}

      {/* Dialog for displaying poster details */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            borderRadius: 3,
          },
        }}
      >
        <DialogTitle>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
          >
            <Stack direction="row" spacing={1} alignItems="center">
              <GroupIcon color="primary" />
              <Typography variant="h6">
                Chi tiết người đăng - Group: {selectedGroup?.groupID}
              </Typography>
            </Stack>
            <IconButton onClick={handleCloseDialog} size="small">
              <CloseIcon />
            </IconButton>
          </Stack>
        </DialogTitle>
        <DialogContent>
          {selectedGroup?.UserGroup && selectedGroup.UserGroup.length > 0 ? (
            <Stack spacing={2} sx={{ mt: 1 }}>
              <Typography variant="body2" color="text.secondary">
                Tổng số người đăng: {selectedGroup.UserGroup.length}
              </Typography>
              {selectedGroup.UserGroup.map((userGroup, userIndex) => (
                <UserGroupRow
                  key={userGroup.id || userIndex}
                  userGroup={userGroup}
                />
              ))}
            </Stack>
          ) : (
            <Typography variant="body2" color="text.secondary">
              Không có dữ liệu người đăng
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} variant="outlined">
            Đóng
          </Button>
        </DialogActions>
      </Dialog>
    </DetailInfoCard>
  );
};

export default CampaignGroupsSection;
