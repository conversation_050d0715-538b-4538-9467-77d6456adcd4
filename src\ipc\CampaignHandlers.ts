import { ipcMain } from 'electron';
import { ICampaignsController } from '../interfaces/ICampaignsController';
import CampaignsController from '../controllers/CampaignController';
import CampaignsServices from '../services/CampaignsServices';
import CampaignConfigServices from '../services/CampaignConfigServices';
import CampaignGroupServices from '../services/CampaignGroupServices';
import CampaignImageServices from '../services/CampaignImageServices';

const configServices = new CampaignConfigServices();
const groupServices = new CampaignGroupServices();
const imageServices = new CampaignImageServices();
const campaignsServices = new CampaignsServices();
const controller: ICampaignsController = new CampaignsController(
  campaignsServices,
  configServices,
  groupServices,
  imageServices,
);

const CampaignHandlers = (): void => {
  ipcMain.handle('Campaigns:createCampaign', async (_e, data) => {
    return controller.createCampaign(data);
  });

  ipcMain.handle('Campaigns:saveCampaignConfig', async (_e, data) => {
    return controller.saveCampaignConfig(data);
  });

  ipcMain.handle('Campaigns:saveCampaignGroup', async (_e, data) => {
    return controller.saveCampaignGroup(data);
  });

  ipcMain.handle('Campaigns:saveCampaignImage', async (_e, data) => {
    return controller.saveCampaignImage(data);
  });

  ipcMain.handle('Campaigns:getAllCampaigns', async (_e, type) => {
    return controller.getAllCampaigns(type);
  });

  ipcMain.handle('Campaigns:getCampaignDetails', async (_e, data) => {
    return controller.getCampaignDetails(data);
  });

  ipcMain.handle('Campaigns:updateCampaign', async (_e, data) => {
    return controller.updateCampaign(data);
  });

  ipcMain.handle('Campaigns:deleteCampaign', async (_e, data) => {
    return controller.deleteCampaign(data);
  });

  ipcMain.handle('Campaigns:bulkDeleteCampaigns', async (_e, data) => {
    return controller.bulkDeleteCampaigns(data);
  });

  ipcMain.handle('Campaigns:getCampaign', (_e, id) => {
    return controller.getCampaign(id);
  });

  ipcMain.handle('Campaigns:getuserpost', (_e, groupID) => {
    return controller.getuserpost(groupID);
  });
};

export default CampaignHandlers;
