import * as XLSX from 'xlsx';

/**
 * Handle file upload and process the content to extract UIDs.
 * @param maxFileSize default is 5MB
 * @param file Excel, csv or .txt
 * @param onSuccess
 * @param onError
 * @returns dataSet and uidString
 */
export const processUploadedFile = (
  maxFileSize: number = 5, // 5MB
  file: File,
  onSuccess: (uids: string[]) => void,
  onError: (errorMessage: string) => void,
) => {
  // Validate file size
  if (file.size > maxFileSize * 1024 * 1024) {
    onError(`Kích thước file không vượt quá ${maxFileSize}MB`);
    return;
  }

  // Validate file type
  const validTypes = ['.txt', '.csv', '.xls', '.xlsx'];
  const fileExtension = file.name
    .slice(file.name.lastIndexOf('.'))
    .toLowerCase();
  if (!validTypes.includes(fileExtension)) {
    onError('Chỉ hỗ trợ file Excel, csv hoặc .txt');
    return;
  }

  const reader = new FileReader();

  reader.onload = (event) => {
    try {
      let uids: string[] = [];

      if (fileExtension === '.txt' || fileExtension === '.csv') {
        //file TXT/CSV
        const text = event.target?.result as string;
        const lines = text
          .split('\n')
          .map((line) => line.trim())
          .filter((line) => line);

        // Check header
        const header = lines[0].toLowerCase();
        if (!header.includes('uid') && !header.includes('uid')) {
          onError('File không có cột tiêu đề UID');
          return;
        }

        // Get data from the first column, skip header and empty cells
        uids = lines
          .slice(1)
          .map((line) => {
            const firstColumn = line.split(',')[0].trim();
            return firstColumn;
          })
          .filter((uid) => uid);
      } else if (fileExtension === '.xls' || fileExtension === '.xlsx') {
        // file Excel
        const data = new Uint8Array(event.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
        const jsonData = XLSX.utils.sheet_to_json<string[]>(firstSheet, {
          header: 1,
        });

        // Check header
        const header = jsonData[0][0]?.toString().toLowerCase();
        if (!header?.includes('uid') && !header?.includes('uid')) {
          onError('File không có cột tiêu đề UID hoặc UID');
          return;
        }

        // Get data from the first column, skip header and empty cells
        uids = jsonData
          .slice(1)
          .map((row) => {
            const firstColumn = row[0]?.toString().trim();
            return firstColumn;
          })
          .filter((uid) => uid);
      }

      // Remove duplicated and return the UIDs
      const uidSet = [...new Set(uids)];
      onSuccess(uidSet);
    } catch (error) {
      onError('Lỗi khi đọc file. Vui lòng kiểm tra định dạng file');
    }
  };

  reader.onerror = () => {
    onError('Lỗi khi đọc file');
  };

  if (fileExtension === '.txt' || fileExtension === '.csv') {
    reader.readAsText(file);
  } else {
    reader.readAsArrayBuffer(file);
  }
};

/**
 * Image preview URL from File object or file path
 * @param file
 * @param filePath - Optional file path for existing images
 * @returns
 */
export const getImagePreviewUrl = (file: File, filePath?: string) => {
  // If file has content (size > 0), use the File object
  if (file.size > 0) {
    return URL.createObjectURL(file);
  }

  // If file is empty but we have a path, convert Windows path to file URL
  if (filePath) {
    const fixed = filePath.replace(/\\/g, '/'); // Windows \ → /
    return `file:///${fixed.split('/').map(encodeURIComponent).join('/')}`;
  }

  // Fallback to File object
  return URL.createObjectURL(file);
};

export const getFileName = (file: File, filePath?: string) => {
  // If file has a name, use it
  if (file.name && file.name !== 'unknown') {
    return file.name;
  }

  // If we have a file path, extract the filename
  if (filePath) {
    return filePath.split(/[\\/]/).pop() || 'unknown';
  }

  return file.name || 'unknown';
};

export const formatFileSize = (file: File, filePath?: string) => {
  const bytes = file.size;

  // If file has no size but we have a path, show "Existing file"
  if (bytes === 0 && filePath) {
    return 'Existing file';
  }

  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
};
