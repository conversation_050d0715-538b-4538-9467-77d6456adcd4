import React from 'react';
import { Chip, useTheme } from '@mui/material';
import { Column } from '../../../components/table/TableConfig';
import { IFBUserResponse } from '../../../../interfaces/IFacebookUser';
import StatusChip from '../../../components/StatusChip';
import PasswordField from '../../../components/PasswordField';
import { getCategoryChipStyle } from '../../../../utils/accountStatus';

interface GetAccountTableColumnsProps {
  passwordVisibility: Record<string, boolean>;
  onTogglePasswordVisibility: (accountId: string) => void;
}

/**
 * Get account table columns configuration
 * @param passwordVisibility - Record of password visibility states
 * @param onTogglePasswordVisibility - Function to toggle password visibility
 * @returns Array of column configurations for account table
 */
export const getAccountTableColumns = ({
  passwordVisibility,
  onTogglePasswordVisibility,
}: GetAccountTableColumnsProps): Column<IFBUserResponse>[] => {
  const theme = useTheme();
  const categoryStyle = getCategoryChipStyle(theme);

  return [
    {
      key: 'username',
      text: 'Tên tài khoản',
      sortable: true,
    },
    {
      key: 'password',
      text: 'Mật khẩu',
      sortable: false,
      render: (value: string, item: IFBUserResponse) => (
        <PasswordField
          password={value}
          isVisible={passwordVisibility[item.id] || false}
          onToggleVisibility={() => onTogglePasswordVisibility(item.id)}
        />
      ),
    },
    {
      key: 'profileId',
      text: 'Profile UID',
      sortable: false,
    },
    {
      key: 'userId',
      text: 'UID Tài khoản',
      sortable: false,
    },
    {
      key: 'category_name',
      text: 'Danh mục',
      sortable: true,
      render: (value: string) => (
        <Chip
          label={value}
          variant="outlined"
          size="small"
          sx={categoryStyle}
        />
      ),
    },
    {
      key: 'status',
      text: 'Trạng thái',
      sortable: false,
      render: (value: string) => <StatusChip status={value} type="account" />,
    },
  ];
};
