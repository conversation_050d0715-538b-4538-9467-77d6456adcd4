import React from 'react';
import { useSelector } from 'react-redux';
import { RouterProvider } from 'react-router-dom';

import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline, StyledEngineProvider } from '@mui/material';

import router from './routes';

import NavigationScroll from './layout/NavigationScroll';
import themes from './themes';
import AlertProvider from './hooks/AlertContext';
import { ModalProvider } from './hooks/ConfirmModalContext';
import EnvironmentManager from '../config/environment';
import { ApiConfig } from '../config/ApiConfig';

function App() {
  const customization = useSelector((state: any) => state.customization);
  const muiTheme = themes(customization);

  // Initialize environment and sync with ApiConfig
  React.useEffect(() => {
    EnvironmentManager.initialize();
    ApiConfig.env = EnvironmentManager.currentEnvironment;
    console.log('Environment initialized:', {
      environment: EnvironmentManager.currentEnvironment,
      apiHost: EnvironmentManager.config.apiHost,
      crmApiHost: EnvironmentManager.config.crmApiHost,
    });
  }, []);

  return (
    <StyledEngineProvider injectFirst>
      <ThemeProvider theme={muiTheme}>
        <CssBaseline />
        <AlertProvider>
          <ModalProvider>
            <NavigationScroll>
              <RouterProvider router={router} />
            </NavigationScroll>
          </ModalProvider>
        </AlertProvider>
      </ThemeProvider>
    </StyledEngineProvider>
  );
}

export default App;
