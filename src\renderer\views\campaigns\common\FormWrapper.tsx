import type { ReactNode } from "react";
import { motion, Variants } from "framer-motion";
import { Box, Typography } from "@mui/material";

type FormWrapperProps = {
  title: string;
  description: string;
  children: ReactNode;
};

const formVariants: Variants = {
  hidden: {
    opacity: 0,
    x: -50,
  },
  visible: {
    opacity: 1,
    x: 0,
  },
  exit: {
    opacity: 0,
    x: 50,
    transition: {
      ease: "easeOut",
    },
  },
};

function FormWrapper({ title, description, children }: FormWrapperProps){
  return (
    <motion.div
      variants={formVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
    >
      <Box m={2} sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
        <Typography
          variant="h5"
          sx={{ fontWeight: 600, color: "inherit" }}
        >
          {title}
        </Typography>
        <Typography
          variant="subtitle1"
          sx={{ color: "gray", fontSize: "0.75rem", fontStyle: "italic" }}
        >
          {description}
        </Typography>
      </Box>
      {children}
    </motion.div>
  );
};

export default FormWrapper;