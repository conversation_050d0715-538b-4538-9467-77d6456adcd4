/**
 * API Configuration for different environments
 * Mirrors the C# ApiConfig implementation
 */

export type Environment = 'dev' | 'test' | 'prod';

export class ApiConfig {
  private static _env: Environment = 'prod';
  private static _bearerToken: string = '';
  private static _sessionId: string = '';

  /**
   * Get or set the current environment
   */
  static get env(): Environment {
    return this._env;
  }

  static set env(value: Environment) {
    this._env = value;
  }

  /**
   * Get or set the bearer token
   */
  static get bearerToken(): string {
    return this._bearerToken;
  }

  static set bearerToken(value: string) {
    this._bearerToken = value;
  }

  /**
   * Get or set the session ID
   */
  static get sessionId(): string {
    return this._sessionId;
  }

  static set sessionId(value: string) {
    this._sessionId = value;
  }

  /**
   * Get the main API host based on current environment
   */
  static get apiHost(): string {
    switch (this._env) {
      case 'dev':
        return 'http://************:8899';
      case 'test':
        return 'https://api-dev.phanmemtop.vn';
      case 'prod':
        return 'https://api.phanmemtop.vn';
      default:
        return 'http://************:8899';
    }
  }

  /**
   * Get the CRM API host based on current environment
   */
  static get crmApiHost(): string {
    switch (this._env) {
      case 'dev':
        return 'http://localhost:3000';
      case 'test':
        return 'https://crm-dev.phanmemtop.vn';
      case 'prod':
        return 'https://crm.phanmemtop.vn';
      default:
        return 'http://localhost:3000';
    }
  }

  // Authentication URLs
  static readonly loginUrl = '/auth/login';
  static readonly logoutUrl = '/auth/logout';
  static readonly refreshTokenUrl = '/auth/refresh';
  static readonly syncSessionUrl = '/auth/sync-session';

  // License URLs
  static readonly licActiveUrl = '/license/active';

  // Chat history URLs
  static readonly generateResponseUrl = '/chat-histories/generate-answer';
  static readonly getChatHistoriesUrl = '/chat-histories/get-chathistories';

  // AI config URLs
  static readonly getAiConfigUrl = '/ai-config/get';
  static readonly updateAiConfigUrl = '/ai-config/update';

  // User URLs
  static readonly registerUrl = '/users/register';

  /**
   * Get full URL for main API endpoint
   */
  static getApiUrl(endpoint: string): string {
    return `${this.apiHost}${endpoint}`;
  }

  /**
   * Get full URL for CRM API endpoint
   */
  static getCrmApiUrl(endpoint: string): string {
    return `${this.crmApiHost}${endpoint}`;
  }

  /**
   * Get authorization header with bearer token
   */
  static getAuthHeader(): Record<string, string> {
    if (!this._bearerToken) {
      return {};
    }
    return {
      Authorization: `Bearer ${this._bearerToken}`,
    };
  }

  /**
   * Clear authentication data
   */
  static clearAuth(): void {
    this._bearerToken = '';
    this._sessionId = '';
  }

  /**
   * Check if user is authenticated
   */
  static get isAuthenticated(): boolean {
    return !!this._bearerToken;
  }

  /**
   * Set environment from string (with validation)
   */
  static setEnvironment(env: string): void {
    if (env === 'dev' || env === 'test' || env === 'prod') {
      this._env = env;
    } else {
      console.warn(`Invalid environment: ${env}. Using default: prod`);
      this._env = 'prod';
    }
  }
}

export default ApiConfig;
