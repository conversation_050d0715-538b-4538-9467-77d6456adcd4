import { ipc<PERSON>ain } from 'electron';
import { IAccountFBController } from '../interfaces/IAccountFBController';
import AccountFBController from '../controllers/AccountFBController';
import CategoryServices from '../services/CategoryServices';
import AccountFBServices from '../services/AccountFBServices';
import AuthServices from '../services/facebook/AuthServices';


const categoryServices = new CategoryServices();
const accountFBServices = new AccountFBServices();
const authServices = new AuthServices();
const auth: IAccountFBController = new AccountFBController(accountFBServices, categoryServices, authServices);

const registerAccountHandlers = (): void => {

  ipcMain.handle('account:findByUserId', (_e, userId) => {
    return auth.findByUserId(userId);
  });

  ipcMain.handle('account:getUser', (_e, id) => {
    return auth.getUser(id);
  });

  ipcMain.handle('account:createUser', (_e, data) => {
    return auth.createUser(data);
  });

  ipcMain.handle('account:getAllUsers', (_e, search) => {
    return auth.getAllUsers(search);
  });

  ipcMain.handle('account:deleteUser', (_e, id) => {
    return auth.deleteUser(id);
  });

  ipcMain.handle('account:updateUser', (_e, data) => {
    return auth.updateUser(data);
  });

  ipcMain.handle('account:launchProfilePage', (_e, id) => {
    return auth.launchProfilePage(id);
  });

  ipcMain.handle('account:getUserByStatus', (_e, status) => {
    return auth.getUserByStatus(status);
  });

  ipcMain.handle('account:bulkDeleteUsers', (_e, ids) => {
    return auth.bulkDeleteUsers(ids);
  });
};

export default registerAccountHandlers;
