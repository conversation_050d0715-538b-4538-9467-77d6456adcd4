/* eslint-disable @typescript-eslint/no-unused-vars */
import { Database } from 'better-sqlite3';
import log from '../utils/logs';
import DatabaseManager from '../db/sqlite';
import { CampaignConfiguration, CampaignConfigurationRequest } from '../interfaces/Campaigns';

export default class CampaignConfigServices {

  private db: Database;

  constructor() {
    this.db = DatabaseManager.getInstance().getDb();
  }

  getConfig(id: string): CampaignConfiguration|null {
    try {
      return this.db
      .prepare(`SELECT * FROM campaignconfiguration WHERE id = ?`)
      .get(id) as CampaignConfiguration;
    } catch (error) {
      return null;
    }
  }

  findByCampaignId(campaignId: string): CampaignConfiguration|null {
    try {
      return this.db
      .prepare(`SELECT * FROM campaignconfiguration WHERE campaign_id = ?`)
      .get(campaignId) as CampaignConfiguration;
    } catch (error) {
      return null;
    }
  }

  createConfig(campaignId: string, data: CampaignConfigurationRequest): number| null {
    let commentContent = null;
    if (data.comment_after_post === true && data.comment_content.trim() !== '') {
      commentContent = data.comment_content;
    }

    try {
      const insertConfig = this.db.prepare(`
        INSERT INTO campaignconfiguration (
          campaign_id,
          delay,
          max_post,
          comment_after_post,
          comment_content,
          is_anonymous,
          is_joingroup,
          tag_friend,
          tagFollowers,
          tagNeubat
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      const result = insertConfig.run(
        campaignId,
        data.delay || 5,
        data.max_post || 1,
        data.comment_after_post || 0,
        commentContent,
        data.is_anonymous || 0,
        data.is_joingroup || 0,
        data.tag_friend || 0,
        data.tagFollowers || 0,
        data.tagNeubat || 0
      );

      return result.lastInsertRowid as number;
    } catch (error) {
      log.error("An error occurred when create campaign configuration", error);
      return null;
    }
  }

  deleteConfig(id: string): boolean {
    try {
      this.db.prepare(`DELETE FROM campaignconfiguration WHERE id = ?`).run(id);
      return true;
    } catch (error) {
      log.error(`An error occurred when delete config id ${id}`, error);
      return false;
    }
  }

  updateConfig(campaignId: string, data: CampaignConfigurationRequest): boolean {
    const config = this.findByCampaignId(campaignId);
    if (!config) {
      return false;
    }
    try {
      const insertStmt = this.db.prepare(`
        UPDATE campaignconfiguration SET 
        delay = ?,
        max_post = ?,
        comment_after_post = ?,
        comment_content = ?,
        is_anonymous = ?,
        is_joingroup = ?,
        tag_friend = ?,
        tagFollowers = ?,
        tagNeubat = ? 
        WHERE id = ?
        `);
      
      const result = insertStmt.run(
        data.delay || 5,
        data.max_post || 1,
        data.comment_after_post || 0,
        data.comment_content || null,
        data.is_anonymous || 0,
        data.is_joingroup || 0,
        data.tag_friend || 0,
        data.tagFollowers || 0,
        data.tagNeubat || 0,
        config.id,
      );

      return true;
    } catch (error) {
      log.error("An error occurred when update campaign config", error);
      return false;
    }
  }
}
