/* eslint-disable react/no-array-index-key */
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Skeleton from '@mui/material/Skeleton';
import Card from '@mui/material/Card';
import Divider from '@mui/material/Divider';
import { Theme } from '@mui/material';

type CampaignTableSkeletonProps = {
  theme: Theme;
};

export default function CampaignTableSkeleton({
  theme,
}: CampaignTableSkeletonProps) {
  return (
    <Box>
      {/* Header Skeleton */}
      <Paper elevation={1} sx={{ p: 3, mb: 3, borderRadius: 3 }}>
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
        >
          <Box>
            <Skeleton variant="text" width={300} height={40} />
            <Skeleton variant="text" width={200} height={24} />
          </Box>
          <Stack direction="row" spacing={2}>
            <Skeleton
              variant="rectangular"
              width={120}
              height={36}
              sx={{ borderRadius: 1 }}
            />
            <Skeleton
              variant="rectangular"
              width={100}
              height={36}
              sx={{ borderRadius: 1 }}
            />
          </Stack>
        </Stack>
      </Paper>

      {/* Search Skeleton */}
      <Paper elevation={1} sx={{ p: 2, mb: 3, borderRadius: 2 }}>
        <Skeleton variant="rectangular" height={56} sx={{ borderRadius: 2 }} />
      </Paper>

      {/* Table Skeleton */}
      <Card elevation={2} sx={{ borderRadius: 3 }}>
        <Box sx={{ p: 2 }}>
          <Skeleton variant="text" width={200} height={32} />
        </Box>
        <Divider />
        {[...Array(5)].map((_, index) => (
          <Box
            key={index}
            sx={{ p: 2, borderBottom: `1px solid ${theme.palette.divider}` }}
          >
            <Stack direction="row" spacing={2} alignItems="center">
              <Skeleton variant="circular" width={24} height={24} />
              <Skeleton variant="text" width={150} />
              <Skeleton variant="text" width={100} />
              <Skeleton
                variant="rectangular"
                width={100}
                height={24}
                sx={{ borderRadius: 1 }}
              />
              <Stack direction="row" spacing={1}>
                <Skeleton variant="circular" width={32} height={32} />
                <Skeleton variant="circular" width={32} height={32} />
                <Skeleton variant="circular" width={32} height={32} />
              </Stack>
            </Stack>
          </Box>
        ))}
      </Card>
    </Box>
  );
}
