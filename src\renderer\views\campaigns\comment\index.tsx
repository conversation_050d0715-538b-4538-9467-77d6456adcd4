import { Box, Container } from "@mui/material";
import { AnimatePresence } from "framer-motion";
import { FormEvent, useState } from "react";
import FormSideBar from "../common/FormSidebar";
import { formPostInit, InputConfig } from "../CampaignConfig";
import FormContent from "../common/FormContent";
import useMultiplestepForm from "../../../hooks/useMultiplestepForm";
import ButtonStepper from "../../../components/buttons/ButtonStepper";
import ButtonText from "../../../components/buttons/ButtonText";
import CommonValidation from "../../../../utils/validation";
import { CampaignDetailsRequest } from "../../../../interfaces/Campaigns";
import FormCommentConfig from "./FormCommentConfig";
import FormImportPost from "./FormImportPost";


const campaignSteps = ['Nội dung', '<PERSON><PERSON><PERSON> hình chiến dịch', 'Nhập danh sách'];
const initSteps =campaignSteps.map((item, index) => ({
  index,
  label: item,
  color: 'primary'
}));

function FormCommentStepper(){
  const {
    currentStep,
    isFirstStep,
    isLastStep,
    goTo,
    nextStep,
    previousStep
  } = useMultiplestepForm(campaignSteps.length);
  const [formData, setFormData] = useState<CampaignDetailsRequest>(formPostInit);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleChange = (
    name: keyof CampaignDetailsRequest,
    value: any,
    type: InputConfig['type'] = 'text'
  ) => {
    setFormData((prev) => {
      if (type === 'number') return { ...prev, [name]: Number(value) };
      if (type === 'checkbox') return { ...prev, [name]: value };
      return { ...prev, [name]: value };
    });
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return <FormContent config={formData} onChange={handleChange} errors={errors}/>;
      case 1:
        return <FormCommentConfig config={formData} onChange={handleChange} />;
      case 2:
      default:
        return <FormImportPost config={formData} onChange={handleChange} />
    }
  };

  /**
   * Validate form data
   * @returns boolean
   */
  const validateData = () => {
    const newErrors: { username?: string; password?: string } = {};
    const [errRequired] = CommonValidation.requiredString(formData.message);
    if (errRequired) {
      newErrors.username = errRequired;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }

  const handleOnSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (validateData()) {
      // handle submit

      setFormData(formPostInit); // reset form
    }
    nextStep();
  };

  return (
    <Container
      maxWidth="lg"
      sx={{
        display: "flex",
        justifyContent: "space-between",
        height: { xs: currentStep === 1 ? "600px" : "500px", md: currentStep === 1 ? "500px" : "500px" },
        width: "91.666%",
        bgcolor: "#262626",
        border: 1,
        borderColor: "#4b5563",
        borderRadius: 2,
        p: 2,
        position: "relative",
        m: 1,
      }}
    >
      <FormSideBar initSteps={initSteps} currentStep={currentStep} goTo={goTo} />
      <Box
        component="main"
        sx={{
          width: { xs: "100%", md: "80%" },
          mt: { md: 5 },
          display: "flex",
          flexDirection: "column",
          height: "100%",
        }}
        >
        <Box
          component="form"
          onSubmit={handleOnSubmit}
          sx={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "space-between",
            height: "100%",
            width: "100%",
          }}
          >
          <AnimatePresence mode="wait">
            {renderStepContent(currentStep)}
          </AnimatePresence>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              width: "100%",
            }}
            >
            <ButtonText
              onClick={previousStep}
              visibility={isFirstStep}
            >
              Quay lại
            </ButtonText>

            <ButtonStepper>
              {isLastStep ? "Khởi chạy" : "Tiếp tục"}
            </ButtonStepper>
          </Box>
        </Box>
      </Box>
    </Container>
  );
};

export default FormCommentStepper;