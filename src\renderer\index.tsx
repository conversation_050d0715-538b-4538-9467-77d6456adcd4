import { createRoot } from 'react-dom/client';
import { configureStore } from '@reduxjs/toolkit';
import { Provider } from 'react-redux';

import App from './App';
import reducer from './store/reducer';

import './assets/scss/style.scss';

const container = document.getElementById('root') as HTMLElement;
const root = createRoot(container);
const store = configureStore({ reducer });
root.render(
  <Provider store={store}>
    <App />
  </Provider>
);
