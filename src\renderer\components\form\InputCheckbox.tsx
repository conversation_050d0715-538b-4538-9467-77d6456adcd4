import FormControlLabel from "@mui/material/FormControlLabel";
import Checkbox from "@mui/material/Checkbox";
import { InputCheckboxProps } from "./formType";

function InputCheckbox ({
  label,
  name,
  checked,
  ...rest
}: InputCheckboxProps) {
  return (
    <FormControlLabel
      control={
        <Checkbox
          checked={checked} 
          name={name}
          {...rest}
        />
      }
      label={label}
    />
  );
};

export default InputCheckbox;