/**
 * E<PERSON>r Handler Utility
 * Provides comprehensive error handling and user-friendly error messages
 */

// Logger interface
interface Logger {
  info: (message: string, data?: any) => void;
  error: (message: string, data?: any) => void;
  warn: (message: string, data?: any) => void;
  debug: (message: string, data?: any) => void;
}

// Create console logger fallback
const createConsoleLogger = (): Logger => ({
  info: (message: string, data?: any) =>
    console.log(`[INFO] ${message}`, data || ''),
  error: (message: string, data?: any) =>
    console.error(`[ERROR] ${message}`, data || ''),
  warn: (message: string, data?: any) =>
    console.warn(`[WARN] ${message}`, data || ''),
  debug: (message: string, data?: any) =>
    console.debug(`[DEBUG] ${message}`, data || ''),
});

// Use different logger based on environment
let log: Logger;
if (typeof window !== 'undefined') {
  // Renderer process - use simple console logger
  log = createConsoleLogger();
} else {
  // Main process - try to use the original logger with dynamic import
  let mainLogger: Logger | null = null;

  // Use a function to get logger to avoid webpack bundling issues
  const getMainLogger = (): Logger => {
    if (mainLogger) return mainLogger;

    try {
      // Use eval to prevent webpack from analyzing this require
      const logsModule = eval('require')('./logs');
      mainLogger = logsModule.default || logsModule;
      return mainLogger!; // We know it's not null here
    } catch (error) {
      // Fallback to console if logs module not available
      mainLogger = createConsoleLogger();
      return mainLogger;
    }
  };

  // Create a proxy logger that lazily loads the main logger
  log = {
    info: (message: string, data?: any) => getMainLogger().info(message, data),
    error: (message: string, data?: any) =>
      getMainLogger().error(message, data),
    warn: (message: string, data?: any) => getMainLogger().warn(message, data),
    debug: (message: string, data?: any) =>
      getMainLogger().debug(message, data),
  };
}

export interface ErrorInfo {
  code?: string;
  message: string;
  details?: any;
  timestamp: string;
  context?: string;
}

export class AppError extends Error {
  public readonly code: string;
  public readonly details?: any;
  public readonly timestamp: string;
  public readonly context?: string;

  constructor(
    message: string,
    code: string = 'UNKNOWN_ERROR',
    details?: any,
    context?: string,
  ) {
    super(message);
    this.name = 'AppError';
    this.code = code;
    this.details = details;
    this.timestamp = new Date().toISOString();
    this.context = context;

    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AppError);
    }
  }

  toJSON(): ErrorInfo {
    return {
      code: this.code,
      message: this.message,
      details: this.details,
      timestamp: this.timestamp,
      context: this.context,
    };
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication failed', details?: any) {
    super(message, 'AUTH_ERROR', details, 'Authentication');
  }
}

export class ValidationError extends AppError {
  constructor(message: string = 'Validation failed', details?: any) {
    super(message, 'VALIDATION_ERROR', details, 'Validation');
  }
}

export class NetworkError extends AppError {
  constructor(message: string = 'Network error occurred', details?: any) {
    super(message, 'NETWORK_ERROR', details, 'Network');
  }
}

export class ServerError extends AppError {
  constructor(message: string = 'Server error occurred', details?: any) {
    super(message, 'SERVER_ERROR', details, 'Server');
  }
}

/**
 * Error Handler Class
 */
export class ErrorHandler {
  /**
   * Handle and log errors
   */
  static handle(error: any, context?: string): ErrorInfo {
    let errorInfo: ErrorInfo;

    if (error instanceof AppError) {
      errorInfo = error.toJSON();
    } else if (error instanceof Error) {
      errorInfo = {
        code: 'UNKNOWN_ERROR',
        message: error.message,
        details: {
          name: error.name,
          stack: error.stack,
        },
        timestamp: new Date().toISOString(),
        context,
      };
    } else {
      errorInfo = {
        code: 'UNKNOWN_ERROR',
        message:
          typeof error === 'string' ? error : 'An unknown error occurred',
        details: error,
        timestamp: new Date().toISOString(),
        context,
      };
    }

    // Log the error
    log.error(`Error in ${errorInfo.context || 'Unknown context'}`, errorInfo);

    return errorInfo;
  }

  /**
   * Get user-friendly error message
   */
  static getUserFriendlyMessage(error: any): string {
    if (error == 401) {
      return 'Đăng nhập thất bại. Sai tài khoản hoặc mật khẩu.';
    }

    return 'Có lỗi xảy ra. Vui lòng thử lại sau.';
  }

  /**
   * Get contextual error message based on error code
   */
  private static getContextualMessage(
    code: string,
    originalMessage: string,
  ): string {
    const errorMessages: Record<string, string> = {
      // Authentication errors
      AUTH_ERROR:
        'Authentication failed. Please check your credentials and try again.',
      INVALID_CREDENTIALS: 'Invalid email or password. Please try again.',
      TOKEN_EXPIRED: 'Your session has expired. Please log in again.',
      UNAUTHORIZED: 'You are not authorized to perform this action.',

      // Validation errors
      VALIDATION_ERROR: 'Please check your input and try again.',
      REQUIRED_FIELD: 'Please fill in all required fields.',
      INVALID_EMAIL: 'Please enter a valid email address.',
      PASSWORD_TOO_SHORT: 'Password must be at least 6 characters long.',
      PASSWORDS_DONT_MATCH: 'Passwords do not match.',

      // Network errors
      NETWORK_ERROR:
        'Network connection failed. Please check your internet connection and try again.',
      REQUEST_TIMEOUT: 'Request timed out. Please try again.',
      CONNECTION_REFUSED:
        'Unable to connect to the server. Please try again later.',

      // Server errors
      SERVER_ERROR: 'Server error occurred. Please try again later.',
      INTERNAL_SERVER_ERROR:
        'Internal server error. Please contact support if the problem persists.',
      SERVICE_UNAVAILABLE:
        'Service is temporarily unavailable. Please try again later.',

      // Registration errors
      EMAIL_ALREADY_EXISTS: 'An account with this email already exists.',
      REGISTRATION_FAILED: 'Registration failed. Please try again.',

      // General errors
      UNKNOWN_ERROR: 'An unexpected error occurred. Please try again.',
      OPERATION_FAILED: 'Operation failed. Please try again.',
    };

    return (
      errorMessages[code] || originalMessage || 'An unexpected error occurred.'
    );
  }

  /**
   * Create error from HTTP response
   */
  static fromHttpResponse(response: any, context?: string): AppError {
    const status = response.status || 500;
    const data = response.data || {};

    let code = 'SERVER_ERROR';
    let message = 'Server error occurred';

    if (status === 401) {
      code = 'UNAUTHORIZED';
      message = 'Authentication required';
    } else if (status === 403) {
      code = 'FORBIDDEN';
      message = 'Access denied';
    } else if (status === 404) {
      code = 'NOT_FOUND';
      message = 'Resource not found';
    } else if (status === 422) {
      code = 'VALIDATION_ERROR';
      message = 'Validation failed';
    } else if (status >= 500) {
      code = 'SERVER_ERROR';
      message = 'Server error occurred';
    }

    // Override with server-provided error info if available
    if (data.code) code = data.code;
    if (data.message) message = data.message;

    return new AppError(message, code, { status, response: data }, context);
  }

  /**
   * Check if error is retryable
   */
  static isRetryable(error: any): boolean {
    if (error instanceof AppError) {
      const retryableCodes = [
        'NETWORK_ERROR',
        'REQUEST_TIMEOUT',
        'CONNECTION_REFUSED',
        'SERVER_ERROR',
        'SERVICE_UNAVAILABLE',
      ];
      return retryableCodes.includes(error.code);
    }

    return false;
  }

  /**
   * Format error for display in UI
   */
  static formatForUI(error: any): {
    title: string;
    message: string;
    severity: 'error' | 'warning' | 'info';
    retryable: boolean;
  } {
    const userMessage = this.getUserFriendlyMessage(error);
    const isRetryable = this.isRetryable(error);

    let title = 'Error';
    let severity: 'error' | 'warning' | 'info' = 'error';

    if (error instanceof AuthenticationError) {
      title = 'Authentication Error';
    } else if (error instanceof ValidationError) {
      title = 'Validation Error';
      severity = 'warning';
    } else if (error instanceof NetworkError) {
      title = 'Connection Error';
    } else if (error instanceof ServerError) {
      title = 'Server Error';
    }

    return {
      title,
      message: userMessage,
      severity,
      retryable: isRetryable,
    };
  }
}

// Export convenience functions
export const handleError = ErrorHandler.handle;
export const getUserFriendlyMessage = ErrorHandler.getUserFriendlyMessage;
export const formatErrorForUI = ErrorHandler.formatForUI;
export const isRetryableError = ErrorHandler.isRetryable;

export default ErrorHandler;
