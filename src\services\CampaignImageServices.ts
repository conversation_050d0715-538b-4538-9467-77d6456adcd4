/* eslint-disable @typescript-eslint/no-unused-vars */
import { Database } from 'better-sqlite3';
import log from '../utils/logs';
import DatabaseManager from '../db/sqlite';
import { Image, ImageRequest } from '../interfaces/Campaigns';

export default class CampaignImageServices {

  private db: Database;

  constructor() {
    this.db = DatabaseManager.getInstance().getDb();
  }

  getAllByCampaignId(campaignId: string|number): Image[] {
    try {
      return this.db
      .prepare(`SELECT * FROM Image WHERE campaign_id = ?`)
      .all(campaignId) as Image[];
    } catch (error) {
      return [];
    }
  }

  createImageList(data: ImageRequest): boolean {
    if (data.imagePaths.length === 0) {
      log.error("Create groups failed because data is empty");
      return false;
    }

    try {
      const insertImage = this.db.prepare(`
        INSERT INTO Image (campaign_id, image)
        VALUES (?, ?)
      `);

      const insertManyImages = this.db.transaction((items: ImageRequest) => {
        items.imagePaths.forEach((image) => {
          insertImage.run(items.campaignId, image).lastInsertRowid as number;
        });
      });

      insertManyImages(data);
      return true;
    } catch (error) {
      log.error("An error occurred when create image list", error);
      return false;
    }
  }

  deleteImageByCampaignId(campaignId: string| number): boolean {
    try {
      this.db.prepare(`DELETE FROM Image WHERE campaign_id = ?`).run(campaignId);
      return true;
    } catch (error) {
      log.error(`An error occurred when delete image list by campaignId ${campaignId}`, error);
      return false;
    }
  }

  updateImageList(data: ImageRequest): boolean {

    const records = this.getAllByCampaignId(data.campaignId);
    if (records && records.length > 0) {
      const isDeleted = this.deleteImageByCampaignId(data.campaignId);
      if (!isDeleted) {
        return false;
      }
    }

    return this.createImageList(data);
  }

}
