import * as React from 'react';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormControl from '@mui/material/FormControl';
import FormLabel from '@mui/material/FormLabel';
import { InputRadioGroupProps } from './formType';

export default function InputRadio({title, name, value, options, onChange}: InputRadioGroupProps) {

  return (
    <FormControl>
      <FormLabel id={name}>{title}</FormLabel>
      <RadioGroup
        aria-labelledby={name}
        name={name}
        value={value}
        onChange={onChange}
      >
        {options.map(
          (item) => (<FormControlLabel value={item.value} control={<Radio />} label={item.label} />))}
      </RadioGroup>
    </FormControl>
  );
}
