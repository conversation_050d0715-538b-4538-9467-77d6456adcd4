import fs from 'fs/promises';
import path from 'path';

export default class ImageServices{
  async copyProfileImage(sourceImagePath: string[]): Promise<string[]> {
  const destDir = path.join(__dirname, '../../../userData', 'Image');
    await fs.mkdir(destDir, { recursive: true });

    const copiedPaths: string[] = [];

    for (const Image of sourceImagePath) {
      const ext = path.extname(Image);
      const baseName = path.basename(Image, ext);

      // Đặt tên file mới có timestamp
      const timestamp = this.getTimestamp();
      const newFileName = `${baseName}_${timestamp}${ext}`;
      const destPath = path.join(destDir, newFileName);

      await fs.copyFile(Image, destPath);
      copiedPaths.push(destPath);

      // Thêm delay 1s để timestamp thay đ<PERSON>i n<PERSON><PERSON> cần
      await new Promise(r => setTimeout(r, 1000));
    }

    return copiedPaths;
  }

  getTimestamp() {
    const now = new Date();
    const yyyy = now.getFullYear();
    const MM = String(now.getMonth() + 1).padStart(2, '0');
    const dd = String(now.getDate()).padStart(2, '0');
    const hh = String(now.getHours()).padStart(2, '0');
    const mm = String(now.getMinutes()).padStart(2, '0');
    const ss = String(now.getSeconds()).padStart(2, '0');
    return `${yyyy}${MM}${dd}_${hh}${mm}${ss}`;
  }
}
