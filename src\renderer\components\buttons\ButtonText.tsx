import Button, { ButtonProps } from "@mui/material/Button";
import { ReactNode } from "react";


interface BtnProps extends ButtonProps {
  children: ReactNode;
  visibility: boolean; 
}

export default function ButtonStepper(props: BtnProps){
  const { children, visibility, ...otherProps } = props;

  const configuration = {
    ...otherProps,
    fullWidth: true
  };

  return (
    <Button
      {...configuration}
      type="button"
      variant="text"
      sx={{
        visibility: visibility ? "hidden" : "visible",
        p: 0,
        color: "primary",
        textTransform: "none",
        "&:hover": {
          backgroundColor: "transparent",
          color: "#fff",
        },
      }}
      disableRipple
    >
      {children}
    </Button>
  );
};
