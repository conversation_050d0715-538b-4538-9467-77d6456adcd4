/**
 * Authentication Hook (Simplified Direct API Approach)
 * Custom hook for authentication using direct API calls
 */

import { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import directAuthService from '../services/DirectAuthService';
import { ErrorHandler } from '../../utils/errorHandler';
import { ApiConfig } from '../../config/ApiConfig';
import {
  LoginRequest,
  RegisterRequest,
  User,
} from '../../interfaces/IAuthentication';

interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  isLoading: boolean;
  error: string | null;
}

export const useAuth = () => {
  const navigate = useNavigate();
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    isLoading: true,
    error: null,
  });

  // Initialize authentication on mount
  useEffect(() => {
    const initAuth = () => {
      try {
        const { isAuthenticated, user } = directAuthService.initializeAuth();
        setAuthState({
          isAuthenticated,
          user,
          isLoading: false,
          error: null,
        });
      } catch (error) {
        setAuthState({
          isAuthenticated: false,
          user: null,
          isLoading: false,
          error: ErrorHandler.getUserFriendlyMessage(error),
        });
      }
    };

    initAuth();
  }, []);

  // Login function
  const login = useCallback(
    async (credentials: LoginRequest) => {
      setAuthState((prev) => ({ ...prev, isLoading: true, error: null }));

      try {
        const result = await directAuthService.login(credentials);

        console.log('result', result);

        if (result.success && result.data) {
          setAuthState({
            isAuthenticated: true,
            user: result.data.user,
            isLoading: false,
            error: null,
          });
          navigate('/dashboard', { replace: true });
          return { success: true };
        } else {
          const errorMessage = ErrorHandler.getUserFriendlyMessage(
            result.error,
          );
          setAuthState((prev) => ({
            ...prev,
            isLoading: false,
            error: errorMessage,
          }));
          return { success: false, error: errorMessage };
        }
      } catch (error) {
        const errorMessage = ErrorHandler.getUserFriendlyMessage(error);
        setAuthState((prev) => ({
          ...prev,
          isLoading: false,
          error: errorMessage,
        }));
        return { success: false, error: errorMessage };
      }
    },
    [navigate],
  );

  // Register function
  const register = useCallback(
    async (userData: RegisterRequest) => {
      setAuthState((prev) => ({ ...prev, isLoading: true, error: null }));

      try {
        const result = await directAuthService.register(userData);

        if (result.success) {
          setAuthState((prev) => ({ ...prev, isLoading: false, error: null }));
          navigate('/pages/login/login3', { replace: true });
          return { success: true };
        } else {
          const errorMessage = ErrorHandler.getUserFriendlyMessage(
            result.error,
          );
          setAuthState((prev) => ({
            ...prev,
            isLoading: false,
            error: errorMessage,
          }));
          return { success: false, error: errorMessage };
        }
      } catch (error) {
        const errorMessage = ErrorHandler.getUserFriendlyMessage(error);
        setAuthState((prev) => ({
          ...prev,
          isLoading: false,
          error: errorMessage,
        }));
        return { success: false, error: errorMessage };
      }
    },
    [navigate],
  );

  // Logout function
  const logout = useCallback(async () => {
    setAuthState((prev) => ({ ...prev, isLoading: true }));

    try {
      await directAuthService.logout();
      setAuthState({
        isAuthenticated: false,
        user: null,
        isLoading: false,
        error: null,
      });
      navigate('/pages/login/login3', { replace: true });
    } catch (error) {
      // Still log out locally even if server request fails
      setAuthState({
        isAuthenticated: false,
        user: null,
        isLoading: false,
        error: null,
      });
      navigate('/pages/login/login3', { replace: true });
    }
  }, [navigate]);

  // Refresh token function
  const refreshToken = useCallback(
    async (app_code: string, machine_name?: string) => {
      try {
        const result = await directAuthService.refreshToken({
          app_code,
          machine_name,
          session: ApiConfig.sessionId,
        });
        if (!result.success) {
          // Token refresh failed, log out user
          await logout();
        }
        return result;
      } catch (error) {
        await logout();
        return {
          success: false,
          error: ErrorHandler.getUserFriendlyMessage(error),
        };
      }
    },
    [logout],
  );

  // Clear error function
  const clearError = useCallback(() => {
    setAuthState((prev) => ({ ...prev, error: null }));
  }, []);

  return {
    // State
    isAuthenticated: authState.isAuthenticated,
    user: authState.user,
    isLoading: authState.isLoading,
    error: authState.error,

    // Actions
    login,
    register,
    logout,
    refreshToken,
    clearError,
  };
};

export default useAuth;
