/* eslint-disable no-await-in-loop */
import { Page } from 'puppeteer-core';
import log from '../../utils/logs';
import FacebookBrowser from './FacebookBrowser';
import { delay } from '../../utils/helper';
import { FBGroup, FBSearchGroupsResponse } from '../../types/FacebookGroup';

export default class GroupSearchServices extends FacebookBrowser {
  private selectorGroupLink: string;

  private selectorTitles: Array<string>;

  constructor() {
    super();
    this.selectorGroupLink =
      'a[href*="/groups/"], a[href*="facebook.com/groups/"]';
    this.selectorTitles = [
      '[role="heading"]',
      'h1',
      'h2',
      'h3',
      'h4',
      'h5',
      'h6',
      'strong',
      'span[dir="auto"]',
      '.x1heor9g',
      '.x1qlqyl8',
      '.x1lli2ws',
      '.x193iq5w',
      '[aria-label]',
      '[title]',
    ];
  }

  /**
   * Search groups on Facebook by value
   * @param profileId
   * @param value
   * @returns GroupSearchResult
   */
  async searchGroups(
    page: Page,
    value: string,
    targetGroup: number,
  ): Promise<FBSearchGroupsResponse> {

    try {
      await page.goto(
        `https://www.facebook.com/search/groups/?q=${encodeURIComponent(value)}`,
        {
          waitUntil: 'networkidle2',
          timeout: 30000,
        },
      );

      // Timeout for load more content
      delay(5000);

      const groups = await this.extractGroupsFromPage(page, targetGroup);

      console.log('Found groups:', groups);

      return { success: true, groups };
    } catch (error: any) {
      log.error('Search group error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Extract uid from url
   * @param url
   * @returns
   */
  private static async extractGroupIdFromUrl(
    url: string,
  ): Promise<string | null> {
    const groupIdMatch = url.match(/\/groups\/(\d+)/);
    if (groupIdMatch) {
      return groupIdMatch[1];
    }
    const groupNameInUrlMatch = url.match(/\/groups\/([a-zA-Z0-9.-]+)/);
    if (groupNameInUrlMatch && !/\d/.test(groupNameInUrlMatch[1])) {
      return null;
    }
    return null;
  }

  /**
   * Extract group name
   * @param page
   * @param link
   * @returns
   */
  private async extractGroupName(
    page: Page,
    link: HTMLAnchorElement,
  ): Promise<string> {
    let groupName = 'Unknown Group';

    const text = await Promise.any(
      this.selectorTitles.map(async (selector) => {
        const nameElement = await page.evaluateHandle(
          (el, sel) => el.querySelector(sel),
          link,
          selector,
        );
        return page.evaluate((el) => el?.textContent?.trim(), nameElement);
      }),
    ).catch(() => null);

    groupName = text || groupName;

    // Get content in href
    if (groupName === 'Unknown Group') {
      const linkText = await page.evaluate(
        (el) => el.textContent?.trim(),
        link,
      );
      if (linkText) {
        groupName = linkText;
      }
    }

    if (groupName === 'Unknown Group') {
      let currentElement = await page.evaluateHandle(
        (el) => el.parentElement,
        link,
      );
      let depth = 0;
      const maxDepth = 5;

      // Move the function outside the loop
      const getTextFromSelector = async (selector: string, element: any) => {
        const nameElement = await page.evaluateHandle(
          (el, sel) => {
            if (el) {
              return el.querySelector(sel);
            }
            return null;
          },
          element,
          selector,
        );
        return page.evaluate((el) => el?.textContent?.trim(), nameElement);
      };

      while (currentElement && depth < maxDepth) {
        const results = await Promise.all(
          // eslint-disable-next-line no-loop-func
          this.selectorTitles.map((selector) =>
            getTextFromSelector(selector, currentElement),
          ),
        );
        const content = results.find((t) => t && t.length > 5) || null;

        groupName = content || groupName;

        if (groupName !== 'Unknown Group') break;
        currentElement = await page.evaluateHandle((el) => {
          if (el) {
            return el.parentElement;
          }
          return null;
        }, currentElement);
        depth += 1;
      }
    }

    if (groupName === 'Unknown Group') {
      const ariaLabel = await page.evaluate(
        (el) => el.getAttribute('aria-label')?.trim(),
        link,
      );
      const title = await page.evaluate(
        (el) => el.getAttribute('title')?.trim(),
        link,
      );
      groupName = ariaLabel || title || groupName;
    }

    return groupName;
  }

  /**
   * Extract group from URL
   * @param page
   * @returns
   */
  private async extractGroupsFromLinks(
    page: Page,
    targetGroupCount: number = 50,
  ): Promise<FBGroup[]> {
    const seenIds = new Set<string>();
    const groups: FBGroup[] = [];

    let previousHeight = 0;
    let retries = 0;
    const MAX_RETRIES = 20;
    const SCROLL_DELAY = 2000; // 2s

    while (groups.length < targetGroupCount && retries < MAX_RETRIES) {
      // Cuộn xuống cuối trang
      const currentHeight = await page.evaluate(() => document.body.scrollHeight);
      await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));

      // Chờ load nội dung mới
      await delay(SCROLL_DELAY);

      // Lấy tất cả thẻ <a> có href chứa '/groups/'
      const groupElements = await page.$$(
        'a[href*="/groups/"]',
      );

      // eslint-disable-next-line no-restricted-syntax
      for (const link of groupElements) {
        try {
          const href = await page.evaluate(
            (el) => (el as HTMLAnchorElement).href,
            link,
          );

          const groupId = await GroupSearchServices.extractGroupIdFromUrl(href);
          if (!groupId || seenIds.has(groupId)) continue; // eslint-disable-line no-continue

          const groupName = await this.extractGroupName(
            page,
            link as unknown as HTMLAnchorElement,
          );

          seenIds.add(groupId);

          const cleanUrl = href.split('?')[0].split('#')[0];
          groups.push({ uid: groupId, groupName, url: cleanUrl });

          console.log(`[+${groups.length}] Đã thêm nhóm: ${groupName}`);
          if (groups.length >= targetGroupCount) break;
        } catch (err) {
          console.error('Lỗi xử lý nhóm:', err);
        }
      }

      if (currentHeight === previousHeight) {
        retries += 1;
        console.log(`Không có nội dung mới được tải. Đang thử lại... (${retries}/${MAX_RETRIES})`);
      } else {
        previousHeight = currentHeight;
        retries = 0;
      }
    }

    console.log(`Hoàn tất. Đã thu thập ${groups.length} nhóm.`);
    return groups;
  }

  /**
   * Extract group from Attributes
   * @param page
   * @returns
   */
  private async extractGroupsFromDataAttributes(
    page: Page,
  ): Promise<FBGroup[]> {
    const groups: FBGroup[] = [];
    const seenIds = new Set<string>();

    const elementsWithData = await page.$$(this.selectorGroupLink);

    await Promise.all(
      elementsWithData.map(async (element) => {
        try {
          const dataHref = await page.evaluate(
            (el) => el.getAttribute('data-href') || el.getAttribute('href'),
            element,
          );
          const dataHovercard = await page.evaluate(
            (el) => el.getAttribute('data-hovercard'),
            element,
          );
          const targetUrl = dataHref || dataHovercard;

          if (targetUrl) {
            const groupId =
              await GroupSearchServices.extractGroupIdFromUrl(targetUrl);
            if (groupId && !seenIds.has(groupId)) {
              seenIds.add(groupId);

              let inferredName = await page.evaluate(
                (el) => el.textContent?.trim(),
                element,
              );
              if (!inferredName || inferredName.length < 5) {
                const nameElement = await page.$eval(
                  '[role="heading"], span[dir="auto"], strong',
                  (el) => el?.textContent?.trim(),
                  element,
                );
                inferredName =
                  nameElement ||
                  (await page.evaluate(
                    (el) => el.getAttribute('aria-label')?.trim(),
                    element,
                  )) ||
                  'Unknown Group (Data Attribute)';
              }
              groups.push({
                uid: groupId,
                groupName: inferredName,
                url: `https://www.facebook.com/groups/${groupId}`,
              });
            }
          }
        } catch (error) {
          console.error('Error processing data attribute element:', error);
        }
      }),
    );

    return groups;
  }

  /**
   * Extract group from page
   * @param page
   * @returns
   */
  private async extractGroupsFromPage(page: Page, targetGroup: number): Promise<FBGroup[]> {
    try {
      const linkGroups = await this.extractGroupsFromLinks(page, targetGroup);
      console.log('Extracted groups from links:', linkGroups.length);
      return linkGroups;
    } catch (error) {
      console.error('General error during group extraction:', error);
      return [];
    }
  }
}
