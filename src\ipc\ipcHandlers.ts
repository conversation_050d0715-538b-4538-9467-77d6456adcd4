import registerAccountHandlers from './accountHandlers';
import registerFacebookHandlers from './facebookHandlers';
import categoryHandlers from './categoryHandlers';
import CampaignHandlers from './CampaignHandlers';
import registerElectronAPIHandlers from './electronAPIHandlers';

export default function registerIPCHandlers(): void {
  categoryHandlers();
  registerAccountHandlers();
  registerFacebookHandlers();
  CampaignHandlers();
  registerElectronAPIHandlers();
}
