import { CampaignDetailsRequest } from '../../../interfaces/Campaigns';

type CampaignType = 'post' | 'comment' | 'profile' | 'all';

interface InputConfig {
  name: keyof CampaignDetailsRequest;
  label: string;
  type: 'text' | 'number' | 'textarea' | 'checkbox' | 'file';
  value: any;
  required?: boolean;
  multiline?: boolean;
  rows?: number;
  errorMessage?: string;
  notched?: boolean;
}

type CampaignConfigProps = {
  config: CampaignDetailsRequest;
  onChange: (
    name: keyof CampaignDetailsRequest,
    value: any,
    type?: InputConfig['type'],
  ) => void;
  goTo?: (index: number) => void;
  errors?: {
    name?: string;
    message?: string;
    groupIds?: string;
    postIds?: string;
    comment_content?: string;
  };
};

const formPostInit: CampaignDetailsRequest = {
  type: 'post',
  name: '',
  status: 'new',

  delay: 5,
  max_post: 1,
  comment_after_post: false,
  comment_content: '',
  is_anonymous: false,
  is_joingroup: false,
  tag_friend: false,
  message: '',

  tagFollowers: false,
  tagNeubat: false,

  imagePaths: [],
  groupIds: [],
};

export {
  CampaignType,
  CampaignDetailsRequest,
  InputConfig,
  CampaignConfigProps,
  formPostInit,
};
