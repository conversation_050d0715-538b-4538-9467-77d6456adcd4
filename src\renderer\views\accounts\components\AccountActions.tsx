import React from 'react';
import LoginIcon from '@mui/icons-material/Login';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import { Action } from '../../../components/table/TableConfig';
import { IFBUserResponse } from '../../../../interfaces/IFacebookUser';

interface AccountActionsProps {
  onOpenBrowser: (account: IFBUserResponse) => void;
  onEdit: (account: IFBUserResponse) => void;
  onDelete: (account: IFBUserResponse) => void;
}

/**
 * Get account-specific action button configurations
 * @param props - Account actions props
 * @returns Array of action configurations
 */
export const getAccountActions = ({
  onOpenBrowser,
  onEdit,
  onDelete,
}: AccountActionsProps): Action<IFBUserResponse>[] => [
  {
    key: 'browser',
    label: 'Mở trình duyệt',
    color: 'success',
    icon: <LoginIcon />,
    onClick: (item: IFBUserResponse) => onOpenBrowser(item),
  },
  {
    key: 'edit',
    label: 'Chỉnh sửa',
    icon: <EditIcon />,
    onClick: (item: IFBUserResponse) => onEdit(item),
  },
  {
    key: 'delete',
    label: 'Xóa',
    color: 'error',
    icon: <DeleteIcon />,
    onClick: (item: IFBUserResponse) => onDelete(item),
  },
];
