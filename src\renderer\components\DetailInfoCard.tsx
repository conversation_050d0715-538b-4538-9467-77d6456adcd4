import React from 'react';
import {
  Card,
  Box,
  Stack,
  Avatar,
  Typography,
  CardContent,
  useTheme,
  alpha,
} from '@mui/material';

interface DetailInfoCardProps {
  title: string;
  subtitle?: string;
  icon: React.ReactNode;
  iconColor?: string;
  children: React.ReactNode;
  headerColor?: string;
}

/**
 * Reusable detail information card component
 */
const DetailInfoCard: React.FC<DetailInfoCardProps> = ({
  title,
  subtitle,
  icon,
  iconColor,
  children,
  headerColor,
}) => {
  const theme = useTheme();
  const finalHeaderColor = headerColor;
  const finalIconColor = iconColor || finalHeaderColor;

  return (
    <Card
      elevation={1}
      sx={{
        borderRadius: 3,
        border: `1px solid ${theme.palette.divider}`,
        overflow: 'hidden',
        mb: 3,
      }}
    >
      <Box
        sx={{
          p: 3,
          borderBottom: `1px solid ${theme.palette.divider}`,
        }}
      >
        <Stack direction="row" spacing={2} alignItems="center">
          <Avatar
            sx={{
              width: 56,
              height: 56,
              backgroundColor: finalIconColor,
            }}
          >
            {icon}
          </Avatar>
          <Box sx={{ flex: 1 }}>
            <Typography
              variant="h5"
              sx={{ fontWeight: 600, mb: subtitle ? 0.5 : 0 }}
            >
              {title}
            </Typography>
            {subtitle && (
              <Typography variant="body2" color="text.secondary">
                {subtitle}
              </Typography>
            )}
          </Box>
        </Stack>
      </Box>

      <CardContent sx={{ p: 3 }}>{children}</CardContent>
    </Card>
  );
};

export default DetailInfoCard;
