import { ipcMain } from 'electron';
import { ICategoryController } from '../interfaces/ICategoryController';
import CategoryController from '../controllers/CategoryController';
import CategoryServices from '../services/CategoryServices';


const categoryServices = new CategoryServices();
const cate: ICategoryController = new CategoryController(categoryServices);

const categoryHandlers = (): void => {

  ipcMain.handle('category:createCategory', (_e, data) => {
    return cate.createCategory(data.name, data.description, data.resourceType);
  });

  ipcMain.handle('category:getAllCategorys', (_e, resourceType?: string) => {
    return cate.getAllCategorys(resourceType);
  });

  ipcMain.handle('category:deleteCategory', (_e, data) => {
    return cate.deleteCategory(data.id);
  });
};

export default categoryHandlers;
