import { ipc<PERSON><PERSON>, BrowserWindow  } from 'electron';
import FacebookController from '../controllers/FacebookController';
import { CampaignProgress } from '../interfaces/Campaigns';

const facebookController = new FacebookController();

const registerFacebookHandlers = (): void => {
  ipcMain.handle('facebook:login', async (event, data) => {
    const response = await facebookController.login(data);
    return response;
  });

  ipcMain.handle('facebook:start', async (event, ListUser, id) => {
    const window = BrowserWindow.fromWebContents(event.sender);

    // <PERSON><PERSON><PERSON> tra nếu cửa sổ tồn tại trước khi gửi tin nhắn
    if (!window) {
      return { success: false, message: 'Ứng dụng đã đóng' };
    }
    const sendProgress = ({message, campaignId, action }: CampaignProgress) => {
      window.webContents.send('campaign-log', { message, campaignId, action });
    };
    await facebookController.start(ListUser, id, sendProgress);
    return { success: true, message: '<PERSON>ế<PERSON> dịch đã hoàn tất.' };
  });

  ipcMain.handle('facebook:searchGroups', async (event, profileId, value, targetGroup) => {
    const response = await facebookController.searchGroups(profileId, value, targetGroup);
    return response;
  });

  ipcMain.handle('facebook:stopbycampaign', async (event, campaignId) => {
    const response = await facebookController.stopbycampaign(campaignId);
    return response;
  });

  ipcMain.handle('facebook:stopAll', async () => {
    const response = await facebookController.stopAll();
    return response;
  });

  ipcMain.handle('facebook:createUserpost', async (event, ListUser, campaignId) => {
    const response = await facebookController.createUserpost(ListUser, campaignId);
    return response;
  });

  ipcMain.handle('facebook:copyProfileImage', async (event, sourceImagePath ) => {
    const response = await facebookController.copyProfileImage( sourceImagePath );
    return response;
  });
};

export default registerFacebookHandlers;
