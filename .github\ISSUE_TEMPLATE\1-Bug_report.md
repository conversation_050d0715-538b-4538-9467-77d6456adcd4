---
name: Bug report
about: You're having technical issues. 🐞
labels: 'bug'
---

<!-- Please use the following issue template or your issue will be closed -->

## Prerequisites

<!-- If the following boxes are not ALL checked, your issue is likely to be closed -->

- [ ] Using npm
- [ ] Using an up-to-date [`main` branch](https://github.com/electron-react-boilerplate/electron-react-boilerplate/tree/main)
- [ ] Using latest version of devtools. [Check the docs for how to update](https://electron-react-boilerplate.js.org/docs/dev-tools/)
- [ ] Tried solutions mentioned in [#400](https://github.com/electron-react-boilerplate/electron-react-boilerplate/issues/400)
- [ ] For issue in production release, add devtools output of `DEBUG_PROD=true npm run build && npm start`

## Expected Behavior

<!--- What should have happened? -->

## Current Behavior

<!--- What went wrong? -->

## Steps to Reproduce

<!-- Add relevant code and/or a live example -->
<!-- Add stack traces -->

1.

2.

3.

4.

## Possible Solution (Not obligatory)

<!--- Suggest a reason for the bug or how to fix it. -->

## Context

<!--- How has this issue affected you? What are you trying to accomplish? -->
<!--- Did you make any changes to the boilerplate after cloning it? -->
<!--- Providing context helps us come up with a solution that is most useful in the real world -->

## Your Environment

<!--- Include as many relevant details about the environment you experienced the bug in -->

- Node version :
- electron-react-boilerplate version or branch :
- Operating System and version :
- Link to your project :

<!---
❗️❗️ Also, please consider donating (https://opencollective.com/electron-react-boilerplate-594) ❗️❗️

Donations will ensure the following:

🔨 Long term maintenance of the project
🛣 Progress on the roadmap
🐛 Quick responses to bug reports and help requests
 -->
