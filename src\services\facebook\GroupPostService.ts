import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer-core';
import log from '../../utils/logs';
import { delay } from '../../utils/helper';
import { FBContentPostRequest, FBGroupPost } from '../../types/FacebookGroup';
import { ResultCommon } from '../../types/Result';
import { FBUser } from '../../types/FacebookUser';

export default class GroupPostService {
  protected fbGroupUrl: string = 'https://www.facebook.com/groups';

  protected textBoxSelector: string = 'div[contenteditable="true"][role="textbox"]';

  protected btnSelector: string = 'div[role="button"][tabindex="0"]';

  protected formPostSelector: string = 'div[role="dialog"] form';

  /**
   * Find group uid from value
   * @param page
   * @param value includes group uid | group name | group link
   * @returns groupUid
   */
  async goToGroup(page: Page, value: string): Promise<{ success: boolean; groupId?: string; error?: string; }> {
    value = value.trim();
    let groupId;
    let groupPath = value;

    // Normalize group URL
    if (!value.startsWith('https')) {
      groupId = value.replace(/\/$/, '');
      groupPath = `${this.fbGroupUrl}/${groupId}`;
    }else {
      groupId = value.replace(this.fbGroupUrl, '').replace(/\/$/, '');
    }

    try {
      await page.goto(groupPath, { waitUntil: 'networkidle2', timeout: 30000 });

      await page.waitForSelector('a[href*="/groups/"]', { timeout: 10000 }).catch(() => {
        log.warn(`Không tìm thấy selector nhóm trong thời gian chờ: ${value}`);
      });

      if (page.isClosed()) {
        log.error('Page is closed during navigation');
        return { success: false, error: 'Trang đã bị đóng trong quá trình điều hướng' };
      }

      const hrefs = await page.evaluate(() => {
        const groupLinks = document.querySelectorAll('a[href*="/groups/"]');
        return Array.from(groupLinks).map(link => (link as HTMLAnchorElement).href);
      });

      if (!hrefs || hrefs.length === 0) {
        log.error(`Không tìm thấy nhóm: ${value}`);
        return { success: false, error: `Không tìm thấy nhóm: ${value}` };
      }
    } catch (error) {
      log.error(`Lỗi khi truy cập nhóm: ${value}`, error);
      return { success: false, error: `Lỗi khi truy cập nhóm: ${value}` };
    }

    // return groupId if value is groupId
    if (groupId && groupId.match(/^\d+$/)) {
      return { success: true, groupId };
    }

    // Extract group ID from links
    try {
      const groupIdFromLinks = await page.evaluate(() => {
        const groupLinks = document.querySelectorAll('a[href*="/groups/"]');
        for (const link of Array.from(groupLinks) as HTMLAnchorElement[]) {
          const match = link.href.match(/\/groups\/(\d+)/);
          if (match) {
            const groupId = match[1];
            return groupId;
          }
        }
        return null;
      });

      if (!groupIdFromLinks) {
        log.error(`Không thể lấy ID của nhóm riêng tư: ${value}`);
        return { success: false, error: `Không thể lấy thông tin của nhóm riêng tư` };
      }
      return { success: true, groupId: groupIdFromLinks };
    } catch (error: any) {
      if (error.name === 'TimeoutError') {
        log.error(`Timeout khi truy cập nhóm: ${value}`, error);
      } else if (error.message.includes('detached Frame')) {
        log.error(`Detached Frame error khi truy cập nhóm: ${value}`, error);
      } else {
        log.error(`Lỗi khi truy cập nhóm: ${value}`, error);
      }
      return { success: false, error: `Lỗi không xác định khi truy cập nhóm: ${value}` };
    }

  }

  /**
   * Search groups on Facebook by value
   * @param profileId
   * @param value
   * @returns GroupSearchResult
   */
  async postToGroup(page: Page, groupId: string, user: FBUser, {message, filePaths = []}: FBContentPostRequest): Promise<ResultCommon> {

    await page.evaluate(() => {
      window.scrollTo(0, 250);
    });

    log.info(`Đã truy cập vào nhóm ${groupId} với user: ${user.profilePath}`);

    try {

      // find post box
      const postBox = await this.findPostBox(page, groupId, user);
      if (!postBox) {
        return { success: false, error: `Nhóm ${groupId} đang để quyền riêng tư` }
      }

      await postBox.click();

      // modal post box
      const selectors = await this.findTextBox(page);
      if (!selectors || !selectors.textBox) {
        return { success: false, error: `Không được phép đăng bài trong nhóm ${groupId}` }
      }

      const { form, textBox } = selectors;

      await delay(1000);
      await textBox.click();

      // paste message
      if (message && message.trim() !== '') {
        await page.evaluate((contentDiv, textContent) => {

        const pasteEvent = new ClipboardEvent('paste', {
          bubbles: true,
          cancelable: true,
          clipboardData: new DataTransfer(),
        });

        pasteEvent.clipboardData!.setData('text/plain', textContent);
        contentDiv.dispatchEvent(pasteEvent);

        }, textBox, message);
      }

      // handle upload file
      if (filePaths.length > 0) {
        const count = await this.postFile(page, filePaths);
        log.info('Number images is uploaded: ', count);
      }

      // submit form
      await page.evaluate((formElement: HTMLFormElement) => {
        const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
        formElement.dispatchEvent(submitEvent);
      }, form as ElementHandle<HTMLFormElement>);

      // wait for post to be submitted
      const isClosed = await page.waitForFunction(() => {
        const dialog = document.querySelector(this.formPostSelector);
        return !dialog;
      }, { timeout: 15000});

      if (!isClosed) {
        log.error('Dialog post box is not closed after submit');
      }

      await delay(3000);

      return { success: true };

    } catch (error: any) {
      log.error('Post to group error:', error);
      return { success: false, error: error.message};
    }
  }


  /**
   * Get post ID after submit on page Activity
   * @param groupId
   * @param user
   * @returns
   */
  async getPostIdAfterSubmit(page: Page, groupId: string, user: FBUser): Promise<{ success: boolean; groupPost?: FBGroupPost }> {

    if (groupId.startsWith('https')) {
      groupId = groupId.replace(/\/$/, '');
    }

    log.info(`groupId: ${groupId}`);

    try {
      await page.goto(`https://www.facebook.com/${user.userId}/allactivity`, { waitUntil: 'networkidle2', timeout: 30000 });

      const postLink = await page.$(
        `a[href*="/groups/${groupId}/"][href*="/permalink/"], a[href*="/groups/${groupId}/"][href*="/pending_posts/"]`
      );

      if (postLink) {
        const href = await postLink.evaluate(el => el.getAttribute('href'));
        if (!href) {
          log.error('Post Id not found in Activity page');
          return { success: false };
        }
        // get pending post
        const matchPending = href.match(new RegExp(`/groups/${groupId}/pending_posts/(\\d+)`));
        if (matchPending) {
          return { success: true, groupPost: { groupId, postId: matchPending[1], status: 'pending' } };
        }

        // get public post
        const matchPermalink = href.match(new RegExp(`/groups/${groupId}/permalink/(\\d+)`));
        if (matchPermalink) {
          return { success: true, groupPost: { groupId, postId: matchPermalink[1], status: 'public' } };
        }
      }

      log.error('Post Id not found in Activity page');
      return { success: false };
    } catch (error) {
      log.error('Error getting post ID after submit:', error);
      return { success: false };
    }
  }


  /**
   * Find post box
   *
   * PostBox will contains user's href if user can post content. Href such as:
   *
   * "https://www.facebook.com/groups/${groupId}/user/${userId}"
   *
   * "https://www.facebook.com/${username}"
   *
   * "https://www.facebook.com/profile.php?id=${userId}"
   *
   * @returns postBox | null
   */
  private async findPostBox(page: Page, groupId: string, user: FBUser) {

    log.info(`Đang tìm hộp đăng bài trong nhóm ${groupId} với user: ${user.profilePath}`);

    await delay(1000);
    const container = await page.evaluateHandle((gId: string, u: FBUser) => {
      const hrefGroup = `https://www.facebook.com/groups/${gId}/user/${u.userId}/`;
      const hrefProfile = u.profilePath;

      const groups: HTMLAnchorElement[] = [];

      const anchors = document.querySelectorAll('a');
      anchors.forEach((link) => {
        if (link.href === hrefProfile || link.href === hrefGroup) {
          groups.push(link);
        }
      });

      if (groups.length === 0) {
        return null;
      }

      const anchor = groups[groups.length -1];
      return anchor.closest('div');

    }, groupId, user);

    if (container.asElement()) {
      const boxContainer = container as ElementHandle;
      const postBox = await boxContainer.$(this.btnSelector);
      return postBox;
    }

    return null;
  }

    /**
   * Find text box
   * @param page
   */
  private async findTextBox(page: Page) {
    let form = null;
    try {
      form = await page.waitForSelector(this.formPostSelector, { timeout: 3000 });
    } catch (error) {
      log.info('timeout wait for text box', error);
    }

    if (form === null) {
      return null;
    }

    const textBox = await form.$(this.textBoxSelector);
    if (!textBox) {
      return null;
    }

    return { form, textBox }
  }

  /**
   * Handle post file
   * @param fileBase64
   * @param fileName
   * @returns number images uploaded
   */
  async postFile(page: Page, filePaths: Array<string>): Promise<number> {
    try {
      const fileInput = await page.$('div[role="dialog"] form input[type="file"][accept*="image/*"]');
      if (!fileInput) {
        log.error('Input upload file not found');
        return 0;
      }

      await fileInput.uploadFile(...filePaths);

      // wait until number images uploaded = file paths length
      await page.waitForFunction(
        (expectedCount) => {
          const images = document.querySelectorAll('div[role="dialog"] form div[role="group"] img');
          return images.length === expectedCount;
        },
        { timeout: 10000 },
        filePaths.length
      );

      const imageCount = await page.evaluate(() => {
        return document.querySelectorAll('div[role="dialog"] form div[role="group"] img').length;
      });

      // dispatch input change for prepare event submit form
      await page.evaluate((input) => {
        const changeEvent = new Event('change', { bubbles: true });
        input.dispatchEvent(changeEvent);
      }, fileInput);

      return imageCount;

    } catch (error: any) {
      log.error('Upload image error:', error);
      return 0;
    }
  }

}
