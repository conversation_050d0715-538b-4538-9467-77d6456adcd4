import React from 'react';
import {
  Paper,
  Stack,
  IconButton,
  Box,
  Typography,
  Chip,
  useTheme,
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import StopIcon from '@mui/icons-material/Stop';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import InfoIcon from '@mui/icons-material/Info';

interface DetailHeaderProps {
  title: string;
  status?: string;
  onBack: () => void;
  statusConfig?: {
    color: 'success' | 'warning' | 'info' | 'default' | 'error';
    label: string;
    icon: string;
  };
}

const getStatusIcon = (iconName: string) => {
  switch (iconName) {
    case 'PlayArrow':
      return <PlayArrowIcon />;
    case 'Stop':
      return <StopIcon />;
    case 'CheckCircle':
      return <CheckCircleIcon />;
    case 'Cancel':
      return <CancelIcon />;
    case 'Info':
    default:
      return <InfoIcon />;
  }
};

/**
 * Reusable detail header component with back navigation and status display
 */
const DetailHeader: React.FC<DetailHeaderProps> = ({
  title,
  status,
  onBack,
  statusConfig,
}) => {
  const theme = useTheme();

  return (
    <Paper
      elevation={0}
      sx={{
        p: 3,
        mb: 3,
        background: `linear-gradient(135deg, ${theme.palette.primary.main || '#1976d2'} 0%, ${theme.palette.primary.dark || '#1565c0'} 100%)`,
        color: 'white',
        borderRadius: 3,
      }}
    >
      <Stack direction="row" alignItems="center" spacing={2}>
        <IconButton
          onClick={onBack}
          sx={{
            color: 'white',
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
            },
          }}
        >
          <ArrowBackIcon />
        </IconButton>
        <Box sx={{ flex: 1 }}>
          <Typography variant="h4" sx={{ fontWeight: 600, color: 'white' }}>
            {title}
          </Typography>
        </Box>
        {/* {status && statusConfig && (
          <Chip
            icon={getStatusIcon(statusConfig.icon)}
            label={statusConfig.label}
            sx={{
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              color: 'white',
              fontWeight: 600,
              '& .MuiChip-icon': {
                color: 'white',
              },
            }}
          />
        )} */}
      </Stack>
    </Paper>
  );
};

export default DetailHeader;
