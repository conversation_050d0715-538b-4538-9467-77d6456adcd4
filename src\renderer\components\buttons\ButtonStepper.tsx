import Button, { ButtonProps } from "@mui/material/Button";
import { ReactNode } from "react";


interface BtnProps extends ButtonProps {
  children: ReactNode;
}

export default function ButtonStepper(props: BtnProps){
  const { children, ...otherProps } = props;

  const configuration = {
    ...otherProps,
    fullWidth: true
  };

  return (
    <Button
      {...configuration}
      type="submit"
      variant="contained"
      sx={{
        bgcolor: "primary",
        color: "#e2e8f0",
        border: 1,
        borderColor: "rgba(0, 0, 0, 0.2)",
        boxShadow: "0 0 0 1px rgba(0, 0, 0, 0.1)",
        borderRadius: "12px",
        textTransform: "none",
        "&:hover": {
          bgcolor: "#649cf7ff",
          color: "#fff",
          boxShadow: "0 0 0 1px rgba(0, 0, 0, 0.1)",
        },
      }}
    >
      {children}
    </Button>
  );
};
