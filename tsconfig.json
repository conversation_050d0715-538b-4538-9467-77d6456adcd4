{
  "compilerOptions": {
    "incremental": true,
    "target": "es2022",
    "module": "node16",
    "lib": [
      "dom",
      "es2022"
    ],
    "jsx": "react-jsx",
    "strict": true,
    "sourceMap": true,
    "moduleResolution": "node16",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,
    "allowJs": true,
    "outDir": ".erb/dll",
    "forceConsistentCasingInFileNames": true,
    "allowImportingTsExtensions": true,
    "noEmit": true,
  },
  "exclude": [
    "test",
    "release/build",
    "release/app/dist",
    ".erb/dll"
  ]
}