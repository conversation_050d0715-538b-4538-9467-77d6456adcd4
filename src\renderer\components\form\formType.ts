import { CheckboxProps } from '@mui/material/Checkbox';
import { BaseSelectProps } from '@mui/material/Select';
import { RadioProps } from '@mui/material';
import { TextFieldProps } from '@mui/material/TextField';
import { ChangeEvent } from 'react';

export interface InputTextProps
  extends Omit<TextFieldProps, 'id' | 'label' | 'name'> {
  id: string;
  label: string;
  name: string;
  errorMessage?: string;
  [key: string]: any; // Để hỗ trợ các props khác
}

export interface InputCheckboxProps extends CheckboxProps {
  label: string;
  name: string;
  checked: boolean;
}

export interface selectOptions {
  value: string | number | readonly string[];
  text: string;
}

export interface FormSelectProps extends BaseSelectProps<string> {
  id?: string;
  label: string;
  value: string;
  name: string;
  options: selectOptions[];
}

export interface InputRadioProps extends RadioProps {
  label: string;
  value: string;
}

export interface InputRadioGroupProps {
  title: string;
  name: string;
  value: string;
  options: Array<InputRadioProps>;
  onChange: (e: ChangeEvent<HTMLInputElement>) => void;
}
