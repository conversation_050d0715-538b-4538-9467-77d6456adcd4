name: Publish

on:
  push:
    branches:
      - main

jobs:
  publish:
    # To enable auto publishing to github, update your electron publisher
    # config in package.json > "build" and remove the conditional below
    if: ${{ github.repository_owner == 'electron-react-boilerplate' }}

    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [macos-latest]

    steps:
      - name: Checkout git repo
        uses: actions/checkout@v3

      - name: Install Node and NPM
        uses: actions/setup-node@v3
        with:
          node-version: 22
          cache: npm

      - name: Install and build
        run: |
          npm install
          npm run postinstall
          npm run build

      - name: Publish releases
        env:
          # The APPLE_* values are used for auto updates signing
          APPLE_APP_SPECIFIC_PASSWORD: ${{ secrets.APPLE_ID_PASS }}
          APPLE_ID: ${{ secrets.APPLE_ID }}
          APPLE_TEAM_ID: ${{ secrets.APPLE_TEAM_ID }}
          CSC_LINK: ${{ secrets.CSC_LINK }}
          CSC_KEY_PASSWORD: ${{ secrets.CSC_KEY_PASSWORD }}
          # This is used for uploading release assets to github
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          npm exec electron-builder -- --publish always --win --mac --linux
