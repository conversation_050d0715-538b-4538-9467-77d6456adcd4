/* eslint-disable react/no-array-index-key */
import { ChangeEvent } from 'react';
import Box from '@mui/material/Box';
import { CampaignConfigProps, InputConfig } from '../CampaignConfig';
import InputCheckbox from '../../../components/form/InputCheckbox';
import InputText from '../../../components/form/InputText';
import FormWrapper from '../common/FormWrapper';
import GeneralConfig from '../common/GeneralConfig';
import { Typography } from '@mui/material';

export default function FormPostGroupConfig({
  config,
  onChange,
  errors,
}: CampaignConfigProps) {
  const inputs: InputConfig[] = [
    {
      name: 'is_anonymous',
      label: 'Đăng ẩn danh',
      type: 'checkbox',
      value: config.is_anonymous,
    },
    {
      name: 'tagFollowers',
      label: 'Tag @nguoitheodoi',
      type: 'checkbox',
      value: config.tagFollowers,
    },
    {
      name: 'tagNeubat',
      label: 'Tag @neubat',
      type: 'checkbox',
      value: config.tagNeubat,
    },
  ];

  return (
    <FormWrapper
      title="Cấu hình đăng bài"
      description="Thiết lập cấu hình cho chiến dịch"
    >
      <GeneralConfig config={config} onChange={onChange} />

      <Box m={2} sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
        <Typography variant="h5" sx={{ flexGrow: 1 }}>
          Cấu hình chi tiết đăng bài
        </Typography>
        <InputCheckbox
          label="Bình luận vào bài viết sau khi đăng"
          name="comment_after_post"
          checked={config.comment_after_post}
          onChange={(e: ChangeEvent<HTMLInputElement>) =>
            onChange('comment_after_post', e.target.checked, 'checkbox')
          }
        />
        <InputText
          id="comment_content"
          type="textarea"
          label="Nội dung bình luận"
          name="comment_content"
          value={config.comment_content}
          onChange={(e: ChangeEvent<HTMLInputElement>) =>
            onChange('comment_content', e.target.value, 'textarea')
          }
          placeholder="Nhập nội dung bình luận"
          multiline
          rows={4}
          disabled={!config.comment_after_post}
          errorMessage={errors?.comment_content}
        />
      </Box>

      <Box m={2}>
        {inputs.map(
          (input, index) =>
            input.type === 'checkbox' && (
              <InputCheckbox
                key={index}
                label={input.label}
                name={input.name}
                checked={input.value}
                onChange={(e: ChangeEvent<HTMLInputElement>) =>
                  onChange(input.name, e.target.checked, input.type)
                }
              />
            ),
        )}
      </Box>
    </FormWrapper>
  );
}
