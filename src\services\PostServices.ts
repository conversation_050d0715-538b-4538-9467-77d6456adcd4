import { Database } from 'better-sqlite3';
import DatabaseManager from '../db/sqlite';
import { Post } from '../interfaces/Post';

export default class PostServices {

  private db: Database;

  constructor() {
    this.db = DatabaseManager.getInstance().getDb();
  }

  createPost(post: Post): void {
    this.db.prepare(
      `INSERT INTO posts (title, message, image) VALUES (?, ?, ?)`,
    ).run(post.title, post.message, post.image);
  }

  getAllPosts(): Post[] {
    return this.db.prepare(`SELECT * FROM posts`).all() as Post[];
  }

  deletePost(id: number): void {
    this.db.prepare(`DELETE FROM posts WHERE id = ?`).run(id);
  }

  updatePost(id: number, post: Post): void {
    this.db.prepare(
      `UPDATE posts SET title = ?, message = ?, image = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
    ).run(post.title, post.message, post.image, id);
  }

  getPostById(id: number): Post | undefined {
    return this.db.prepare(`SELECT * FROM posts WHERE id = ?`).get(id) as
      | Post
      | undefined;
  }
}
