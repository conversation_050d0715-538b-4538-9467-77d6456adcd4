import React, { createContext, useState, useCallback, useMemo } from 'react';
import ConfirmModal, { ConfirmModalProps } from '../components/modals/ConfirmModal';

interface ModalContextType {
  openConfirmModal: (props: Omit<ConfirmModalProps, 'open' | 'onClose'>) => void;
}

export const ModalContext = createContext<ModalContextType | undefined>(undefined);

export function ModalProvider({ children }: { children: React.ReactNode }) {
  const [confirmModalProps, setConfirmModalProps] = useState<ConfirmModalProps | null>(null);

  const openConfirmModal = useCallback(
    ({ title, content, actions, onSubmit }: Omit<ConfirmModalProps, 'open' | 'onClose'>) => {
      setConfirmModalProps({
        open: true,
        title,
        content,
        actions,
        onSubmit,
        onClose: () => setConfirmModalProps(null),
      });
    },
    []
  );

  const modalContextValue = useMemo(
      () => ({
        openConfirmModal,
      }),
      [openConfirmModal]
    );

  return (
    <ModalContext.Provider value={modalContextValue}>
      {children}
      {confirmModalProps && <ConfirmModal {...confirmModalProps} />}
    </ModalContext.Provider>
  );
};

// Hook get ModalContext
export const useModal = () => {
  const context = React.useContext(ModalContext);
  if (!context) {
    throw new Error('useModal must be used within a ModalProvider');
  }
  return context;
};