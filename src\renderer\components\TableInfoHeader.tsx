import React from 'react';
import {
  Box,
  Typography,
  Stack,
  Chip,
  IconButton,
  Tooltip,
  alpha,
  useTheme,
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';

interface TableInfoHeaderProps {
  title: string;
  totalCount: number;
  selectedCount?: number;
  onRefresh?: () => void;
  subtitle?: string;
}

/**
 * Reusable table info header component
 */
const TableInfoHeader: React.FC<TableInfoHeaderProps> = ({
  title,
  totalCount,
  selectedCount = 0,
  onRefresh,
  subtitle,
}) => {
  const theme = useTheme();

  return (
    <Box
      sx={{
        p: 2,
        backgroundColor: alpha(
          theme.palette.primary.main || '#1976d2',
          0.04,
        ),
        borderBottom: `1px solid ${theme.palette.divider}`,
      }}
    >
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
      >
        <Stack direction="row" alignItems="center" spacing={2}>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 600 }}>
              {title}
            </Typography>
            <Typography
              sx={{ color: 'text.secondary', fontSize: '0.75rem', pt: 0.5 }}
            >
              {subtitle || `Tổng cộng ${totalCount} mục`}
            </Typography>
          </Box>
          {selectedCount > 0 && (
            <Chip
              label={`${selectedCount} đã chọn`}
              color="primary"
              size="small"
              variant="outlined"
            />
          )}
        </Stack>
        <Stack direction="row" spacing={1}>
          {onRefresh && (
            <Tooltip title="Làm mới">
              <IconButton size="small" onClick={onRefresh}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          )}
        </Stack>
      </Stack>
    </Box>
  );
};

export default TableInfoHeader;
