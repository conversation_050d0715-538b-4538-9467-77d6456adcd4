import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  TextF<PERSON>,
  But<PERSON>,
  <PERSON><PERSON>,
  Step,
  StepLabel,
} from '@mui/material';

const steps = ['Nhập từ khoá/link nhóm', '<PERSON><PERSON><PERSON> nhận & hoàn tất'];

export default function ScanGroup() {
  const [activeStep, setActiveStep] = useState(0);

  const handleNext = () => setActiveStep((prev) => prev + 1);
  const handleBack = () => setActiveStep((prev) => prev - 1);

  return (
    <Box>
      <Stepper activeStep={activeStep} sx={{ mb: 3 }}>
        {steps.map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>
      {activeStep === 0 && (
        <Box>
          <Typography sx={{ mb: 2 }}>Nhập từ khoá hoặc link nhóm:</Typography>
          <TextField label="Từ khoá hoặc link nhóm" fullWidth sx={{ mb: 2 }} />
        </Box>
      )}
      {activeStep === 1 && (
        <Box>
          <Typography sx={{ mb: 2 }}>
            Xác nhận thông tin và hoàn tất quét nhóm.
          </Typography>
        </Box>
      )}
      <Box sx={{ mt: 2 }}>
        <Button disabled={activeStep === 0} onClick={handleBack} sx={{ mr: 1 }}>
          Quay lại
        </Button>
        {activeStep < steps.length - 1 ? (
          <Button variant="contained" onClick={handleNext}>
            Tiếp tục
          </Button>
        ) : (
          <Button variant="contained" color="success">
            Hoàn tất
          </Button>
        )}
      </Box>
    </Box>
  );
}
