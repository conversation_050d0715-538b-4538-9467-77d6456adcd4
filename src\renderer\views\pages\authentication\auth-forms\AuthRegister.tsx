import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';

import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Checkbox from '@mui/material/Checkbox';
import FormControl from '@mui/material/FormControl';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormHelperText from '@mui/material/FormHelperText';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import InputLabel from '@mui/material/InputLabel';
import OutlinedInput from '@mui/material/OutlinedInput';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import Alert from '@mui/material/Alert';
import CircularProgress from '@mui/material/CircularProgress';

import * as Yup from 'yup';
import { Formik } from 'formik';

import VisibilityOff from '@mui/icons-material/VisibilityOff';
import Visibility from '@mui/icons-material/Visibility';
import AnimateButton from '../../../../components/extended/AnimateButton';
import { strengthColor, strengthIndicator } from '../../../../../utils/password-strength';
import useAuth from '../../../../hooks/useAuth';

// ===========================|| FIREBASE - REGISTER ||=========================== //
interface tempColor {
  label: string;
  color: string;
}

function AuthRegister({ ...others }) {
  const theme = useTheme();
  const matchDownSM = useMediaQuery(theme.breakpoints.down('md'));
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [checked, setChecked] = useState(true);

  // Use the simplified auth hook
  const { register, isLoading, error, clearError } = useAuth();

  const [strength, setStrength] = useState(0);
  const [level, setLevel] = useState<tempColor>();

  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const handleClickShowConfirmPassword = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  const handleMouseDownPassword = (event:any) => {
    event.preventDefault();
  };

  const changePassword = (value: any) => {
    const temp = strengthIndicator(value);
    setStrength(temp);
    setLevel(strengthColor(temp));
  };

  const handleRegister = async (values: any) => {
    clearError(); // Clear any previous errors
    await register({
      email: values.email,
      password: values.password,
      password_confirmation: values.password_confirmation,
      phone: values.phone,
      full_name: values.full_name,
      app_code: values.app_code,
      referal_code: values.referal_code || '',
    });
  };

  useEffect(() => {
    changePassword('');
  }, []);

  return (
    <Formik
      initialValues={{
        email: '',
        password: '',
        password_confirmation: '',
        phone: '',
        full_name: '',
        app_code: '',
        referal_code: '',
        submit: null
      }}
      validationSchema={Yup.object().shape({
        email: Yup.string().email('Must be a valid email').max(255).required('Email is required'),
        password: Yup.string().min(6, 'Password must be at least 6 characters').max(255).required('Password is required'),
        password_confirmation: Yup.string()
          .oneOf([Yup.ref('password')], 'Passwords must match')
          .required('Password confirmation is required'),
        phone: Yup.string().required('Phone number is required'),
        full_name: Yup.string().required('Full name is required'),
        app_code: Yup.string().required('App code is required'),
        referal_code: Yup.string() // Optional field
      })}
      onSubmit={handleRegister}
    >
      {({ errors, handleBlur, handleChange, handleSubmit, isSubmitting, touched, values }) => (
        <form noValidate onSubmit={handleSubmit} {...others}>
          {/* Full Name Field */}
          <FormControl fullWidth error={Boolean(touched.full_name && errors.full_name)} sx={{ ...theme.typography.customInput }}>
            <InputLabel htmlFor="outlined-adornment-fullname-register">Full Name</InputLabel>
            <OutlinedInput
              id="outlined-adornment-fullname-register"
              type="text"
              value={values.full_name}
              name="full_name"
              onBlur={handleBlur}
              onChange={handleChange}
              label="Full Name"
              inputProps={{}}
            />
            {touched.full_name && errors.full_name && (
              <FormHelperText error id="standard-weight-helper-text-fullname-register">
                {errors.full_name}
              </FormHelperText>
            )}
          </FormControl>

          {/* Phone Field */}
          <FormControl fullWidth error={Boolean(touched.phone && errors.phone)} sx={{ ...theme.typography.customInput }}>
            <InputLabel htmlFor="outlined-adornment-phone-register">Phone Number</InputLabel>
            <OutlinedInput
              id="outlined-adornment-phone-register"
              type="tel"
              value={values.phone}
              name="phone"
              onBlur={handleBlur}
              onChange={handleChange}
              label="Phone Number"
              inputProps={{}}
            />
            {touched.phone && errors.phone && (
              <FormHelperText error id="standard-weight-helper-text-phone-register">
                {errors.phone}
              </FormHelperText>
            )}
          </FormControl>
          {/* Email Field */}
          <FormControl fullWidth error={Boolean(touched.email && errors.email)} sx={{ ...theme.typography.customInput }}>
            <InputLabel htmlFor="outlined-adornment-email-register">Email Address</InputLabel>
            <OutlinedInput
              id="outlined-adornment-email-register"
              type="email"
              value={values.email}
              name="email"
              onBlur={handleBlur}
              onChange={handleChange}
              label="Email Address"
              inputProps={{}}
            />
            {touched.email && errors.email && (
              <FormHelperText error id="standard-weight-helper-text-email-register">
                {errors.email}
              </FormHelperText>
            )}
          </FormControl>

          {/* Password Field */}
          <FormControl fullWidth error={Boolean(touched.password && errors.password)} sx={{ ...theme.typography.customInput }}>
            <InputLabel htmlFor="outlined-adornment-password-register">Password</InputLabel>
            <OutlinedInput
              id="outlined-adornment-password-register"
              type={showPassword ? 'text' : 'password'}
              value={values.password}
              name="password"
              label="Password"
              onBlur={handleBlur}
              onChange={(e) => {
                handleChange(e);
                changePassword(e.target.value);
              }}
              endAdornment={
                <InputAdornment position="end">
                  <IconButton
                    aria-label="toggle password visibility"
                    onClick={handleClickShowPassword}
                    onMouseDown={handleMouseDownPassword}
                    edge="end"
                    size="large"
                  >
                    {showPassword ? <Visibility /> : <VisibilityOff />}
                  </IconButton>
                </InputAdornment>
              }
              inputProps={{}}
            />
            {touched.password && errors.password && (
              <FormHelperText error id="standard-weight-helper-text-password-register">
                {errors.password}
              </FormHelperText>
            )}
          </FormControl>

          {/* Password Confirmation Field */}
          <FormControl fullWidth error={Boolean(touched.password_confirmation && errors.password_confirmation)} sx={{ ...theme.typography.customInput }}>
            <InputLabel htmlFor="outlined-adornment-password-confirmation-register">Confirm Password</InputLabel>
            <OutlinedInput
              id="outlined-adornment-password-confirmation-register"
              type={showConfirmPassword ? 'text' : 'password'}
              value={values.password_confirmation}
              name="password_confirmation"
              label="Confirm Password"
              onBlur={handleBlur}
              onChange={handleChange}
              endAdornment={
                <InputAdornment position="end">
                  <IconButton
                    aria-label="toggle password confirmation visibility"
                    onClick={handleClickShowConfirmPassword}
                    onMouseDown={handleMouseDownPassword}
                    edge="end"
                    size="large"
                  >
                    {showConfirmPassword ? <Visibility /> : <VisibilityOff />}
                  </IconButton>
                </InputAdornment>
              }
              inputProps={{}}
            />
            {touched.password_confirmation && errors.password_confirmation && (
              <FormHelperText error id="standard-weight-helper-text-password-confirmation-register">
                {errors.password_confirmation}
              </FormHelperText>
            )}
          </FormControl>

          {strength !== 0 && (
            <FormControl fullWidth>
              <Box sx={{ mb: 2 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid>
                    <Box style={{ backgroundColor: level?.color }} sx={{ width: 85, height: 8, borderRadius: '7px' }} />
                  </Grid>
                  <Grid>
                    <Typography variant="subtitle1" fontSize="0.75rem">
                      {level?.label}
                    </Typography>
                  </Grid>
                </Grid>
              </Box>
            </FormControl>
          )}

          {/* App Code Field */}
          <FormControl fullWidth error={Boolean(touched.app_code && errors.app_code)} sx={{ ...theme.typography.customInput }}>
            <InputLabel htmlFor="outlined-adornment-appcode-register">App Code</InputLabel>
            <OutlinedInput
              id="outlined-adornment-appcode-register"
              type="text"
              value={values.app_code}
              name="app_code"
              onBlur={handleBlur}
              onChange={handleChange}
              label="App Code"
              inputProps={{}}
            />
            {touched.app_code && errors.app_code && (
              <FormHelperText error id="standard-weight-helper-text-appcode-register">
                {errors.app_code}
              </FormHelperText>
            )}
          </FormControl>

          {/* Referral Code Field (Optional) */}
          <FormControl fullWidth error={Boolean(touched.referal_code && errors.referal_code)} sx={{ ...theme.typography.customInput }}>
            <InputLabel htmlFor="outlined-adornment-referalcode-register">Referral Code (Optional)</InputLabel>
            <OutlinedInput
              id="outlined-adornment-referalcode-register"
              type="text"
              value={values.referal_code}
              name="referal_code"
              onBlur={handleBlur}
              onChange={handleChange}
              label="Referral Code (Optional)"
              inputProps={{}}
            />
            {touched.referal_code && errors.referal_code && (
              <FormHelperText error id="standard-weight-helper-text-referalcode-register">
                {errors.referal_code}
              </FormHelperText>
            )}
          </FormControl>

          <Grid container alignItems="center" justifyContent="space-between">
            <Grid>
              <FormControlLabel
                control={
                  <Checkbox checked={checked} onChange={(event) => setChecked(event.target.checked)} name="checked" color="primary" />
                }
                label={
                  <Typography variant="subtitle1">
                    Agree with &nbsp;
                    <Typography variant="subtitle1" component={Link} to="#">
                      Terms & Condition.
                    </Typography>
                  </Typography>
                }
              />
            </Grid>
          </Grid>
          {(errors.submit || error) && (
            <Box sx={{ mt: 3 }}>
              {errors.submit && <FormHelperText error>{errors.submit}</FormHelperText>}
              {error && (
                <Alert severity="error" sx={{ mt: 1 }}>
                  {error}
                </Alert>
              )}
            </Box>
          )}

          <Box sx={{ mt: 2 }}>
            <AnimateButton>
              <Button
                disableElevation
                disabled={isSubmitting || isLoading || !checked}
                fullWidth
                size="large"
                type="submit"
                variant="contained"
                color="secondary"
                startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : null}
              >
                {isLoading ? 'Creating Account...' : 'Sign up'}
              </Button>
            </AnimateButton>
          </Box>
        </form>
      )}
    </Formik>
  );
}

export default AuthRegister;
