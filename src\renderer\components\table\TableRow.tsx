import TableRow from '@mui/material/TableRow';
import Checkbox from '@mui/material/Checkbox';
import TableCell from '@mui/material/TableCell';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import { DataItem, RowProps } from './TableConfig';

export default function TableRowCustom<T extends DataItem>({
  item,
  columns,
  actions,
  selected,
  onSelect,
}: RowProps<T>) {
  return (
    <TableRow hover key={item.id}>
      <TableCell padding="checkbox">
        <Checkbox checked={selected} onChange={() => onSelect(item.id)} />
      </TableCell>

      {columns.map((cell) => (
        <TableCell key={String(cell.key)}>
          {cell.render
            ? cell.render(item[cell.key], item)
            : (item[cell.key] ?? '---')}
        </TableCell>
      ))}

      <TableCell sx={{ whiteSpace: 'nowrap', display: 'flex', gap: 1 }}>
        {actions.map((action) => {
          const isHidden = action.hidden?.(item) ?? false;
          if (isHidden) return null;
          return (
            <Tooltip key={action.key} title={action.label}>
              <span>
                <IconButton
                  size={action.size ? action.size : 'small'}
                  color={action.color ? action.color : 'primary'}
                  aria-label={action.label}
                  onClick={() => action.onClick(item)}
                  disabled={action.disabled ? action.disabled(item) : false}
                >
                  {action.icon}
                </IconButton>
              </span>
            </Tooltip>
          );
        })}
      </TableCell>
    </TableRow>
  );
}
