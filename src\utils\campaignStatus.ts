import { alpha } from '@mui/material';
import { Theme } from '@mui/material/styles';

export type CampaignStatus = 'new' | 'running' | 'done' | 'stopped';

export interface StatusConfig {
  label: string;
  color: 'info' | 'primary' | 'success' | 'error' | 'default';
  bgColor: string;
  textColor: string;
  borderColor: string;
}

/**
 * Get status configuration for campaign status display
 * @param status - Campaign status
 * @param theme - Material-UI theme
 * @returns Status configuration object
 */
export const getStatusConfig = (status: string, theme: Theme): StatusConfig => {
  switch (status) {
    case 'draft':
      return {
        label: 'Bản nháp',
        color: 'info' as const,
        bgColor: alpha(theme.palette.info.main || '#0288d1', 0.1),
        textColor: theme.palette.info.dark || '#01579b',
        borderColor: alpha(theme.palette.info.main || '#0288d1', 0.3),
      };
    case 'new':
      return {
        label: 'Mớ<PERSON>',
        color: 'info' as const,
        bgColor: alpha(theme.palette.info.main || '#0288d1', 0.1),
        textColor: theme.palette.info.dark || '#01579b',
        borderColor: alpha(theme.palette.info.main || '#0288d1', 0.3),
      };
    case 'running':
      return {
        label: 'Đang chạy',
        color: 'primary' as const,
        bgColor: alpha(theme.palette.primary.main || '#1976d2', 0.1),
        textColor: theme.palette.primary.dark || '#1565c0',
        borderColor: alpha(theme.palette.primary.main || '#1976d2', 0.3),
      };
    case 'done':
      return {
        label: 'Hoàn thành',
        color: 'success' as const,
        bgColor: alpha(theme.palette.success.main || '#4caf50', 0.1),
        textColor: theme.palette.success.dark || '#2e7d32',
        borderColor: alpha(theme.palette.success.main || '#4caf50', 0.3),
      };
    case 'stopped':
      return {
        label: 'Đã dừng',
        color: 'error' as const,
        bgColor: alpha(theme.palette.error.main || '#f44336', 0.1),
        textColor: theme.palette.error.dark || '#c62828',
        borderColor: alpha(theme.palette.error.main || '#f44336', 0.3),
      };
    default:
      return {
        label: status,
        color: 'default' as const,
        bgColor: alpha(theme.palette.grey[500] || '#9e9e9e', 0.1),
        textColor: theme.palette.grey[700] || '#616161',
        borderColor: alpha(theme.palette.grey[500] || '#9e9e9e', 0.3),
      };
  }
};

/**
 * Get campaign type label in Vietnamese
 * @param type - Campaign type
 * @returns Vietnamese label
 */
export const getCampaignTypeLabel = (type: string): string => {
  switch (type) {
    case 'post':
      return 'Đăng bài nhóm';
    case 'comment':
      return 'Bình luận';
    case 'profile':
      return 'Đăng bài trang cá nhân';
    default:
      return 'Tất cả';
  }
};

/**
 * Filter campaigns based on search query
 * @param campaigns - Array of campaigns
 * @param searchQuery - Search query string
 * @returns Filtered campaigns
 */
export const filterCampaigns = <
  T extends { name: string; type?: string; status?: string },
>(
  campaigns: T[],
  searchQuery: string,
): T[] => {
  if (!searchQuery) return campaigns;

  const query = searchQuery.toLowerCase();
  return campaigns.filter(
    (campaign) =>
      campaign.name.toLowerCase().includes(query) ||
      campaign.type?.toLowerCase().includes(query) ||
      campaign.status?.toLowerCase().includes(query),
  );
};

/**
 * Get campaign detail status configuration for display
 * @param status - Campaign status
 * @returns Status configuration object
 */
export const getCampaignDetailStatusConfig = (status?: string) => {
  switch (status?.toLowerCase()) {
    case 'active':
    case 'running':
      return {
        color: 'success' as const,
        label: 'Đang chạy',
        icon: 'PlayArrow',
      };
    case 'stopped':
    case 'paused':
      return {
        color: 'warning' as const,
        label: 'Đã dừng',
        icon: 'Stop',
      };
    case 'done':
    case 'completed':
    case 'true':
      return {
        color: 'info' as const,
        label: 'Hoàn thành',
        icon: 'CheckCircle',
      };
    case 'new':
      return {
        color: 'default' as const,
        label: 'Mới tạo',
        icon: 'Info',
      };
    case 'draft':
      return {
        color: 'default' as const,
        label: 'Bản nháp',
        icon: 'Info',
      };
    case 'error':
    case 'failed':
    case 'false':
      return {
        color: 'error' as const,
        label: 'Lỗi',
        icon: 'Cancel',
      };
    default:
      return {
        color: 'default' as const,
        label: 'Không xác định',
        icon: 'Info',
      };
  }
};
