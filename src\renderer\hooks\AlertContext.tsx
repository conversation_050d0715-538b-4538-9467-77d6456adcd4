import React, { createContext, useContext, useMemo, useState } from 'react';
import { Snackbar, Alert, useTheme } from '@mui/material';

type AlertProps = {
  children: React.ReactNode;
}

type AlertState = {
  message: string;
  severity: 'error' | 'warning' | 'info' | 'success';
  open: boolean;
}

const defaultAlert: AlertState = {
  message: '',
  severity: 'info',
  open: false,
}

interface AlertContextType {
  showAlert: (message: string, severity?: 'error' | 'warning' | 'info' | 'success') => void;
  hideAlert: (event?: React.SyntheticEvent | Event, reason?: string) => void;
}

export const AlertContext = createContext<AlertContextType>({
  showAlert: () => {},
  hideAlert: () => {},
});

export default function AlertProvider({ children }: AlertProps) {
  const [alert, setAlert] = useState<AlertState>(defaultAlert);
  const theme = useTheme()

  const showAlert = (message: string, severity: 'error' | 'warning' | 'info' | 'success' = 'info') => {
    setAlert({ message, severity, open: true });
  };

  const hideAlert = (event?: React.SyntheticEvent | Event, reason?: string) => {
    if (reason === 'clickaway') return;
    setAlert((prev) => ({ ...prev, open: false }));
  };

  const alertContextValue = useMemo(
    () => ({
      showAlert,
      hideAlert,
    }),
    []
  );

  return (
    <AlertContext.Provider value={alertContextValue}>
      {children}
      <Snackbar
        open={alert.open}
        autoHideDuration={5000}
        onClose={hideAlert}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert onClose={hideAlert} severity={alert.severity} 
          sx={{
            width: '100%',
            bgcolor: () => {
              if (alert.severity === 'error') {
                return theme.palette.error.main;
              }
              if(alert.severity === 'warning') {
                return theme.palette.error.light;
              }
              if(alert.severity === 'success') {
                return theme.palette.success.lighter;
              }
              return theme.palette.primary.main;
            },
            color: 'white',
            '& .MuiAlert-icon': {
              color: 'white',
            },
          }}>
          {alert.message}
        </Alert>
      </Snackbar>
    </AlertContext.Provider>
  );
};

export const useAlert = () => {
  const context = useContext(AlertContext);
  if (context === undefined) {
    throw new Error('useAlert must be used within an AlertProvider');
  }
  return context;
};