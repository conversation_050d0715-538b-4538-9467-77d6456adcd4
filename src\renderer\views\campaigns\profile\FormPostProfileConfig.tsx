import { ChangeEvent } from 'react';
import { CampaignConfigProps, InputConfig } from "../CampaignConfig";
import InputCheckbox from '../../../components/form/InputCheckbox';
import InputText from '../../../components/form/InputText';
import FormWrapper from '../common/FormWrapper';
import GeneralConfig from '../common/GeneralConfig';

export default function FormPostProfileConfig({ config, onChange }: CampaignConfigProps) {
  const inputs: InputConfig[] = [
    { 
      name: 'tag_friend', 
      label: 'Nhập từ khóa để tag bạn bè', 
      type: 'text',
      value: config.tag_friend,
    },
    { 
      name: 'comment_content', 
      label: 'Nội dung bình luận', 
      type: 'text',
      value: config.comment_content,
    },
    { 
      name: 'tagFollowers', 
      label: 'Tag @nguoitheodoi', 
      type: 'checkbox',
      value: config.tagFollowers,
    },
    { 
      name: 'tagNeubat', 
      label: 'Tag @neubat', 
      type: 'checkbox',
      value: config.tagNeubat,
    },
  ];

  return (
    <FormWrapper title='Cấu hình đăng bài cá nhân' description='Thiết lập cấu hình đăng bài trên trang cá nhân'>
      <GeneralConfig config={config} onChange={onChange}/> 
      <InputCheckbox
        label='Tag bạn bè theo từ khóa'
        id='tag_friend'
        name='tag_friend'
        checked={config.tag_friend}
        onChange={(e) => onChange('tag_friend', e.target.checked ? '' : undefined, 'checkbox')}
      />

      {config.tag_friend !== undefined && inputs.map((input) => (
        input.type === 'checkbox' ? (
          <InputCheckbox 
            label={input.label}
            id={input.name}
            name={input.name}
            checked={!!config[input.name]}
            onChange={(e) => onChange(input.name, e.target.checked, input.type)}
            />
        ) : (
          <InputText
            id={input.name}
            label={input.label}
            name={input.name}
            type={input.type}
            value={config[input.name] || ''}
            onChange={(e: ChangeEvent<HTMLInputElement>) => onChange(input.name, e.target.value, input.type)}
          />
        )
      ))}
    </FormWrapper>
  );
}