# Facebook Post Management API Documentation

## API Overview

This Electron application provides a comprehensive API for managing Facebook accounts, campaigns, posts, and categories through IPC (Inter-Process Communication) channels. The API follows a controller-service architecture pattern where each controller handles specific business logic domains.

## Architecture

The application uses Electron's IPC system for secure communication between the main and renderer processes:

- **Frontend (Renderer Process)**: React-based UI
- **Backend (Main Process)**: Controllers and Services
- **Communication**: IPC channels via `contextBridge` and `ipcRenderer`

## Authentication & Authorization

Currently, the application does not implement traditional HTTP authentication. Access is controlled through Electron's security model with context isolation and sandboxed renderer processes.

## Common Response Format

All API responses follow a consistent structure:

```typescript
// Success Response
{
  success: boolean;
  data?: any;
  message?: string;
}

// Error Response
{
  success: boolean;
  error?: string;
  message?: string;
}
```

## Rate Limiting

No explicit rate limiting is implemented as this is a desktop application with local database operations.

---

## Account Management API

### Base Channel: `account:`

#### Get User by ID

- **Channel**: `account:getUser`
- **Method**: IPC Handle
- **Parameters**:
  - `id` (string): User ID
- **Response**:
  ```typescript
  {
    success: boolean;
    data?: IFBUserResponse;
  }
  ```
- **Example**:
  ```javascript
  const result = await window.account.getUser('123');
  ```

#### Find User by Facebook User ID

- **Channel**: `account:findByUserId`
- **Parameters**:
  - `userId` (string): Facebook User ID
- **Response**:
  ```typescript
  {
    success: boolean;
    data?: IFBUser;
  }
  ```

#### Create User

- **Channel**: `account:createUser`
- **Parameters**:
  - `data` (IFBUserRequest): User creation data
  ```typescript
  interface IFBUserRequest {
    username: string;
    password: string;
    profileId?: string;
    categoryId: string;
    userId?: string;
    status: string;
  }
  ```
- **Response**:
  ```typescript
  {
    success: boolean;
    error?: string;
    data?: IFBUserResponse;
  }
  ```

#### Get All Users

- **Channel**: `account:getAllUsers`
- **Parameters**:
  - `search?` (string): Optional search term
- **Response**: `IFBUserResponse[]`

#### Update User

- **Channel**: `account:updateUser`
- **Parameters**:
  - `data` (IFBUser): Complete user data
- **Response**:
  ```typescript
  {
    success: boolean;
    error?: string;
    data?: IFBUserResponse;
  }
  ```

#### Delete User

- **Channel**: `account:deleteUser`
- **Parameters**:
  - `id` (string): User ID
- **Response**:
  ```typescript
  {
    success: boolean;
  }
  ```

#### Bulk Delete Users

- **Channel**: `account:bulkDeleteUsers`
- **Parameters**:
  - `ids` (string[]): Array of user IDs
- **Response**: `boolean`

#### Get Users by Status

- **Channel**: `account:getUserByStatus`
- **Parameters**:
  - `status` (string): User status ('active', 'inactive', 'running')
- **Response**: `Promise<IFBUserResponse[]>`

#### Launch Profile Page

- **Channel**: `account:launchProfilePage`
- **Parameters**:
  - `id` (string|number): User ID
- **Response**:
  ```typescript
  {
    success: boolean;
    page?: Page; // Puppeteer Page object
  }
  ```

---

## Campaign Management API

### Base Channel: `Campaigns:`

#### Create Campaign

- **Channel**: `Campaigns:createCampaign`
- **Parameters**:
  - `data` (CampaignRequest): Campaign data
  ```typescript
  interface CampaignRequest {
    id?: string;
    type: string;
    name: string;
    message?: string;
    status?: string;
    created_at?: string;
    updated_at?: string;
  }
  ```
- **Response**:
  ```typescript
  {
    success: boolean;
    data?: Campaign;
    error?: string;
  }
  ```

#### Save Campaign Configuration

- **Channel**: `Campaigns:saveCampaignConfig`
- **Parameters**:
  - `data` (CampaignConfigurationRequest): Configuration data
  ```typescript
  interface CampaignConfigurationRequest {
    campaignId: string;
    delay: number;
    max_post: number;
    comment_after_post: boolean;
    comment_content: string;
    is_anonymous: boolean;
    is_joingroup: boolean;
    tag_friend: boolean;
    tagFollowers: boolean;
    tagNeubat: boolean;
    status?: string;
  }
  ```
- **Response**:
  ```typescript
  {
    success: boolean;
    error?: string;
  }
  ```

#### Save Campaign Groups

- **Channel**: `Campaigns:saveCampaignGroup`
- **Parameters**:
  - `data` (GroupRequest): Group data
  ```typescript
  interface GroupRequest {
    campaignId: string;
    groupIds: string[];
    status?: string;
  }
  ```

#### Save Campaign Images

- **Channel**: `Campaigns:saveCampaignImage`
- **Parameters**:
  - `data` (ImageRequest): Image data
  ```typescript
  interface ImageRequest {
    imagePaths: string[];
    campaignId: string;
  }
  ```

#### Get All Campaigns

- **Channel**: `Campaigns:getAllCampaigns`
- **Parameters**:
  - `type?` (string): Optional campaign type filter
- **Response**: `Campaign[]`

#### Get Campaign Details

- **Channel**: `Campaigns:getCampaignDetails`
- **Parameters**:
  - `id` (string): Campaign ID
- **Response**: `CampaignDetails | null`

#### Update Campaign

- **Channel**: `Campaigns:updateCampaign`
- **Parameters**:
  - `data` (CampaignRequest): Updated campaign data

#### Delete Campaign

- **Channel**: `Campaigns:deleteCampaign`
- **Parameters**:
  - `id` (number): Campaign ID
- **Response**:
  ```typescript
  {
    success: boolean;
    message: string;
  }
  ```

#### Bulk Delete Campaigns

- **Channel**: `Campaigns:bulkDeleteCampaigns`
- **Parameters**:
  - `ids` (string[]): Array of campaign IDs
- **Response**: `boolean`

#### Get Campaign

- **Channel**: `Campaigns:getCampaign`
- **Parameters**:
  - `id` (string): Campaign ID
- **Response**:
  ```typescript
  {
    success: boolean;
    data?: Campaign;
  }
  ```

#### Get User Post Data

- **Channel**: `Campaigns:getuserpost`
- **Parameters**:
  - `groupID` (string): Group ID
- **Response**: `UserGroup`

---

## Category Management API

### Base Channel: `category:`

#### Create Category

- **Channel**: `category:createCategory`
- **Parameters**:
  - `data` (object): Category data
  ```typescript
  {
    name: string;
    description: string;
    resourceType: string;
  }
  ```
- **Response**:
  ```typescript
  {
    success: boolean;
    data?: Category;
  }
  ```

#### Get All Categories

- **Channel**: `category:getAllCategorys`
- **Parameters**:
  - `resourceType?` (string): Optional resource type filter
- **Response**: `Category[]`

#### Delete Category

- **Channel**: `category:deleteCategory`
- **Parameters**:
  - `data` (object): Deletion data
  ```typescript
  {
    id: number;
  }
  ```
- **Response**:
  ```typescript
  {
    success: boolean;
  }
  ```

---

## Facebook Operations API

### Base Channel: `facebook:`

#### Login to Facebook

- **Channel**: `facebook:login`
- **Parameters**:
  - `data` (object): Login credentials
  ```typescript
  {
    username: string;
    password: string;
    profileId: string;
  }
  ```
- **Response**: `FBUserLoginResponse`

#### Start Campaign

- **Channel**: `facebook:start`
- **Parameters**:
  - `ListUser` (IFBUser[]): Array of users
  - `id` (string): Campaign ID
- **Response**:
  ```typescript
  {
    success: boolean;
    message: string;
  }
  ```

#### Search Groups

- **Channel**: `facebook:searchGroups`
- **Parameters**:
  - `profileId` (string): Profile ID
  - `value` (string): Search term
  - `targetGroup` (number): Target group count
- **Response**: `FBSearchGroupsResponse`

#### Stop Campaign

- **Channel**: `facebook:stopbycampaign`
- **Parameters**:
  - `campaignId` (string): Campaign ID
- **Response**:
  ```typescript
  {
    success: boolean;
    message: string;
  }
  ```

#### Stop All Campaigns

- **Channel**: `facebook:stopAll`
- **Response**:
  ```typescript
  {
    success: boolean;
    message: string;
  }
  ```

---

## Post Management API

### Base Channel: `post:` (Currently Not Registered)

**Note**: The Post Controller exists but is not currently registered in the IPC handlers. To enable these endpoints, add `registerPostHandlers()` to `src/ipc/ipcHandlers.ts`.

#### Create Post

- **Channel**: `post:create`
- **Parameters**:
  - `post` (Post): Post data
  ```typescript
  interface Post {
    id?: number;
    title: string;
    message: string;
    image: string;
    created_at?: string;
    updated_at?: string;
  }
  ```
- **Response**:
  ```typescript
  {
    success: boolean;
    message: string;
  }
  ```

#### Get All Posts

- **Channel**: `post:getAll`
- **Parameters**: None
- **Response**: `Promise<Post[]>`

#### Get Post by ID

- **Channel**: `post:getById`
- **Parameters**:
  - `id` (number): Post ID
- **Response**: `Promise<Post | undefined>`

#### Update Post

- **Channel**: `post:update`
- **Parameters**:
  - `data` (object): Update data
  ```typescript
  {
    id: number;
    post: Post;
  }
  ```
- **Response**:
  ```typescript
  {
    success: boolean;
    message: string;
  }
  ```

#### Delete Post

- **Channel**: `post:delete`
- **Parameters**:
  - `id` (number): Post ID
- **Response**:
  ```typescript
  {
    success: boolean;
    message: string;
  }
  ```

---

## File System API

### Base Channel: `dialog:` and `fs:`

#### Show Open Dialog

- **Channel**: `dialog:showOpenDialog`
- **Parameters**:
  - `options` (object): Dialog options
- **Response**: Electron dialog result

#### Show Save Dialog

- **Channel**: `dialog:showSaveDialog`
- **Parameters**:
  - `options` (object): Dialog options
- **Response**: Electron dialog result

#### Read File

- **Channel**: `fs:readFile`
- **Parameters**:
  - `filePath` (string): File path
- **Response**: `Buffer` (file content)

#### Write File

- **Channel**: `fs:writeFile`
- **Parameters**:
  - `filePath` (string): File path
  - `data` (Buffer): File data
- **Response**:
  ```typescript
  {
    success: boolean;
    path: string;
  }
  ```

#### Check File Exists

- **Channel**: `fs:exists`
- **Parameters**:
  - `filePath` (string): File path
- **Response**: `boolean`

#### Get File Stats

- **Channel**: `fs:getFileStats`
- **Parameters**:
  - `filePath` (string): File path
- **Response**:
  ```typescript
  {
    size: number;
    isFile: boolean;
    isDirectory: boolean;
    mtime: Date;
    ctime: Date;
  }
  ```

---

## Data Models

### IFBUser

```typescript
interface IFBUser {
  id: string;
  username: string;
  password: string;
  profileId: string;
  userId?: string;
  status: string;
  categoryId: string;
}
```

### IFBUserResponse

```typescript
interface IFBUserResponse extends IFBUser {
  category_name: string;
}
```

### Campaign

```typescript
interface Campaign {
  id: string;
  type: string;
  name: string;
  status: string;
  message: string;
  created_at?: string;
  updated_at?: string;
}
```

### Category

```typescript
interface Category {
  category_id: string;
  category_name: string;
  category_description: string;
  category_resourceType: string;
}
```

### Post

```typescript
interface Post {
  id?: number;
  title: string;
  message: string;
  image: string;
  created_at?: string;
  updated_at?: string;
}
```

---

## Error Handling

### Common Error Codes

- **Database Errors**: Handled gracefully with try-catch blocks
- **Validation Errors**: Returned in response with `error` field
- **Facebook API Errors**: Propagated from Facebook services
- **File System Errors**: Thrown as exceptions for fs operations

### Error Response Format

```typescript
{
  success: false;
  error?: string;
  message?: string;
}
```

---

## Usage Examples

### Creating a Facebook Account

```javascript
const accountData = {
  username: '<EMAIL>',
  password: 'password123',
  profileId: 'profile_123',
  categoryId: 'cat_1',
  status: 'active',
};

const result = await window.account.createUser(accountData);
if (result.success) {
  console.log('Account created:', result.data);
} else {
  console.error('Error:', result.error);
}
```

### Starting a Campaign

```javascript
const users = await window.account.getAllUsers();
const activeUsers = users.filter((u) => u.status === 'active');

const result = await window.facebook.start(activeUsers, 'campaign_123');
console.log(result.message);
```

### Creating a Campaign

```javascript
const campaignData = {
  type: 'post',
  name: 'My Campaign',
  message: 'Hello World!',
  status: 'draft',
};

const result = await window.Campaigns.createCampaign(campaignData);
if (result.success) {
  console.log('Campaign created:', result.data);
}
```

---

## Real-time Events

### Campaign Progress Events

The application provides real-time campaign progress updates through IPC events:

#### Event: `campaign-log`

- **Type**: IPC Event (one-way communication)
- **Data**:
  ```typescript
  interface CampaignProgress {
    message: string;
    campaignId: string;
    action?: 'running' | 'stopped' | 'done';
  }
  ```
- **Usage**:
  ```javascript
  // Listen for campaign progress updates
  window.electronAPI.onCampaignLog((data) => {
    console.log(`Campaign ${data.campaignId}: ${data.message}`);
    if (data.action === 'done') {
      console.log('Campaign completed!');
    }
  });
  ```

---

## Notes

1. **Post Controller**: The PostController exists but is not currently registered in the IPC handlers, so post-related endpoints are not available. To enable them, add `registerPostHandlers()` to `src/ipc/ipcHandlers.ts`.

2. **Real-time Updates**: Campaign progress is communicated via IPC events (`campaign-log`) for real-time UI updates during campaign execution.

3. **File Operations**: File system operations are available for handling images and other media files used in campaigns.

4. **Security**: The application uses Electron's security best practices with context isolation and sandboxed renderer processes.

5. **Database**: All data is stored locally using SQLite database through better-sqlite3.

6. **Facebook Integration**: The application uses Puppeteer for Facebook automation, requiring valid Facebook credentials for posting operations.

7. **Campaign Execution**: Campaigns can be started, stopped, and monitored in real-time. Multiple campaigns can run concurrently with individual stop controls.

8. **Profile Management**: Each Facebook account is associated with a browser profile for session management and automation.
