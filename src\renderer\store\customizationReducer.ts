import config from '../config';
import * as actionTypes from './actions';

export const initialState = {
  isOpen: [],
  defaultId: 'default',
  fontFamily: config.fontFamily,
  borderRadius: config.borderRadius,
  appTheme: 'light',
  opened: true,
};

// ==============================|| CUSTOMIZATION REDUCER ||============================== //

// eslint-disable-next-line default-param-last
const customizationReducer = (state = initialState, action: any) => {
  let id;
  switch (action.type) {
    case actionTypes.MENU_OPEN:
      id = action.id;
      return {
        ...state,
        isOpen: [id],
      };
    case actionTypes.SET_MENU:
      return {
        ...state,
        opened: action.opened,
      };
    case actionTypes.SET_FONT_FAMILY:
      return {
        ...state,
        fontFamily: action.fontFamily,
      };
    case actionTypes.SET_BORDER_RADIUS:
      return {
        ...state,
        borderRadius: action.borderRadius,
      };
    case actionTypes.SET_APP_THEME:
      return {
        ...state,
        appTheme: action.appTheme,
      };
    default:
      return state;
  }
};

export default customizationReducer;
