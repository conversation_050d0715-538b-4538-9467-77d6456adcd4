/* eslint-disable @typescript-eslint/no-unused-vars */
import { Page } from 'puppeteer-core';
import { delay } from './helper';


/**
 * Type text to input
 * @param page
 * @param selector
 * @param text
 */
export async function typeToInput(page: Page, selector: string, text: string) {
  await delay(2000);
  await page.evaluate((sel) => {
    const input = document.querySelector(sel) as HTMLInputElement;
    if (input) {
      input.value = '';
    }
  }, selector);
  await page.click(selector);
  await delay(1000);
  await page.type(selector, text, { delay: 100 });
  await delay(500);
  await page.mouse.move(-150, -80);
}

/**
 * Extract username from profile url facebook
 * @param url
 * @returns username or empty
 */
export function extractUsernameFromUrl(url: string): string {
  try {
    const parsedUrl = new URL(url);
    const pathSegments = parsedUrl.pathname.split('/').filter(segment => segment.length > 0);
    
    if (parsedUrl.hostname.includes('facebook.com') && pathSegments.length > 0) {
      return pathSegments[0];
    }
    return '';
  } catch (error) {
    return '';
  }
}
