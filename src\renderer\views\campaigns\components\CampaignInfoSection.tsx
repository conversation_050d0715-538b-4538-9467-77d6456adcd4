import React from 'react';
import {
  Grid,
  Paper,
  Stack,
  Typography,
  useTheme,
  alpha,
} from '@mui/material';
import ScheduleIcon from '@mui/icons-material/Schedule';
import { Campaign } from '../../../../interfaces/Campaigns';
import { formatDate } from '../../../../utils/format';
import DetailInfoCard from '../../../components/DetailInfoCard';
import { getCampaignDetailStatusConfig } from '../../../../utils/campaignStatus';

interface CampaignInfoSectionProps {
  campaign: Campaign;
}

/**
 * Campaign information display section
 */
const CampaignInfoSection: React.FC<CampaignInfoSectionProps> = ({
  campaign,
}) => {
  const theme = useTheme();
  const statusConfig = getCampaignDetailStatusConfig(campaign.status);

  return (
    <DetailInfoCard
      title={campaign.name || 'Không có tên'}
      subtitle={`Loại: ${campaign.type || 'Không xác định'} • ID: ${campaign.id || 'N/A'}`}
      icon={<ScheduleIcon />}
    >
      <Grid container spacing={3}>
        <Grid size={{ xs: 12, sm: 6 }}>
          <Paper
            sx={{
              p: 2,
              borderRadius: 2,
              backgroundColor: alpha(
                theme.palette.grey[50] || '#fafafa',
                0.8,
              ),
              border: `1px solid ${alpha(theme.palette.grey[300] || '#e0e0e0', 0.5)}`,
            }}
          >
            <Stack
              direction="row"
              alignItems="center"
              spacing={1}
              sx={{ mb: 1 }}
            >
              <ScheduleIcon
                sx={{ color: 'text.secondary', fontSize: 20 }}
              />
              <Typography variant="subtitle2" color="text.secondary">
                Ngày tạo
              </Typography>
            </Stack>
            <Typography variant="body1" sx={{ fontWeight: 500 }}>
              {formatDate(campaign.created_at || '')}
            </Typography>
          </Paper>
        </Grid>
        <Grid size={{ xs: 12, sm: 6 }}>
          <Paper
            sx={{
              p: 2,
              borderRadius: 2,
              backgroundColor: alpha(
                theme.palette.grey[50] || '#fafafa',
                0.8,
              ),
              border: `1px solid ${alpha(theme.palette.grey[300] || '#e0e0e0', 0.5)}`,
            }}
          >
            <Stack
              direction="row"
              alignItems="center"
              spacing={1}
              sx={{ mb: 1 }}
            >
              <ScheduleIcon
                sx={{ color: 'text.secondary', fontSize: 20 }}
              />
              <Typography variant="subtitle2" color="text.secondary">
                Cập nhật lần cuối
              </Typography>
            </Stack>
            <Typography variant="body1" sx={{ fontWeight: 500 }}>
              {campaign.updated_at
                ? formatDate(campaign.updated_at, 'datetime')
                : '---'}
            </Typography>
          </Paper>
        </Grid>
      </Grid>
    </DetailInfoCard>
  );
};

export default CampaignInfoSection;
