import { Page } from 'puppeteer-core';
import { Category } from '../interfaces/Categorys';
import { IFBUser, IFBUserResponse } from '../interfaces/IFacebookUser';
import { FBSearchGroupsResponse } from '../types/FacebookGroup';
import { FBUserLoginResponse } from '../types/FacebookUser';
import {
  CampaignDetails,
  Campaign,
  CampaignProgress,
  CampaignConfigurationRequest,
  UserGroup,
  ImageRequest,
  GroupRequest,
  CampaignRequest,
} from '../interfaces/Campaigns';

declare global {
  interface Window {
    facebookWeb: {
      login: (data: {
        username: string;
        password: string;
        profileId: string;
      }) => Promise<FBUserLoginResponse>;

      searchGroups: (
        profileId: string,
        value: string,
      ) => Promise<FBSearchGroupsResponse>;

      start: (
        ListUser: IFBUser[],
        id: string,
      ) => Promise<{ success: boolean; message: string }>;

      stopbycampaign: (
        campaignId: string,
      ) => Promise<{ success: boolean; message: string }>;

      stopAll: () => Promise<{ success: boolean; message: string }>;

      onProgress: (callback: (data: CampaignProgress) => void) => () => void;

      createUserpost: (
        ListUser: IFBUser[],
        campaignId: string,
      ) => Promise<{ success: boolean; message: string }>;

      copyProfileImage: (sourceImagePath: string[]) => Promise<{success: boolean; source: string[]}>
    };

    account: {
      findByUserId: (
        userId: string,
      ) => Promise<{ success: boolean; data?: IFBUser }>;
      getUser: (
        id: string | number,
      ) => Promise<{ success: boolean; data?: IFBUserResponse }>;
      createUser: (data: {
        username: string;
        password: string;
        profileId: string;
        categoryId: string;
        userId: string;
        status: string;
      }) => Promise<{ success: boolean; data?: IFBUserResponse }>;
      getAllUsers: (search?: string) => Promise<IFBUserResponse[]>;
      deleteUser: (id: string | number) => Promise<{ success: boolean }>;
      updateUser: (
        data: IFBUser,
      ) => Promise<{
        success: boolean;
        error?: string;
        data?: IFBUserResponse;
      }>;
      launchProfilePage: (
        id: string | number,
      ) => Promise<{ success: boolean; page?: Page }>;
      getUserByStatus: (status: string) => Promise<IFBUserResponse[]>;
      bulkDeleteUsers: (ids: string[]) => Promise<boolean>;
    };

    category: {
      createCategory: (
        data: Category,
      ) => Promise<{ success: boolean; data?: Category }>;
      getAllCategorys: (resourceType: string) => Promise<Category[]>;
      deleteCategory: (id: number) => Promise<{ success: boolean }>;
    };

    Campaigns: {
      createCampaign: (
        data: CampaignRequest,
      ) => Promise<{ success: boolean; data?: Campaign; error?: string }>;
      saveCampaignConfig(
        data: CampaignConfigurationRequest,
      ): Promise<{ success: boolean; error?: string }>;
      saveCampaignGroup(
        data: GroupRequest,
      ): Promise<{ success: boolean; error?: string }>;
      saveCampaignImage(
        data: ImageRequest,
      ): Promise<{ success: boolean; error?: string }>;

      getAllCampaigns(type?: string): Promise<Campaign[]>;
      getCampaignDetails(id: string): Promise<CampaignDetails | null>;
      updateCampaign(
        data: CampaignRequest,
      ): Promise<{ success: boolean; data?: Campaign; error?: string }>;
      deleteCampaign(id: string): Promise<{ success: boolean }>;
      bulkDeleteCampaigns(id: string[]): Promise<boolean>;
      getCampaign: (
        id: string,
      ) => Promise<{ success: boolean; data?: Campaign }>;
      getuserpost: (groupID: string) => UserGroup;
    };

    electronAPI: {
      dialog: {
        showOpenDialog: (options: {
          properties?: string[];
          filters?: { name: string; extensions: string[] }[];
          defaultPath?: string;
        }) => Promise<{
          canceled: boolean;
          filePaths: string[];
        }>;
        showSaveDialog: (options: {
          defaultPath?: string;
          filters?: { name: string; extensions: string[] }[];
        }) => Promise<{
          canceled: boolean;
          filePath: string;
        }>;
      };
      fs: {
        readFile: (filePath: string) => Promise<Buffer>;
        writeFile: (
          filePath: string,
          data: Buffer,
        ) => Promise<{ success: boolean; path: string }>;
        exists: (filePath: string) => Promise<boolean>;
        getFileStats: (filePath: string) => Promise<{
          size: number;
          isFile: boolean;
          isDirectory: boolean;
          mtime: Date;
          ctime: Date;
        }>;
      };
    };
  }
}
