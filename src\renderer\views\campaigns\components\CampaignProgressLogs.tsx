import React, { useRef, useEffect, useState, useCallback } from 'react';
import {
  Card,
  Box,
  Stack,
  Avatar,
  Typography,
  CardContent,
  Chip,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  useTheme,
  alpha,
  Fab,
  Fade,
  Snackbar,
  Alert,
  Button,
} from '@mui/material';
import {
  Info as InfoIcon,
  PlayArrow as RunningIcon,
  CheckCircle as DoneIcon,
  Message as DefaultIcon,
  KeyboardArrowDown as ArrowDownIcon,
} from '@mui/icons-material';

interface ProgressLog {
  message: string;
  campaignId: string;
  action?: 'running' | 'done' | undefined;
}

interface CampaignProgressLogsProps {
  logs: ProgressLog[];
  currentCampaignId: string;
  onCampaignComplete?: () => void;
}

/**
 * Get styling configuration based on action type
 */
const getActionConfig = (
  theme: any,
  action?: 'running' | 'done' | undefined,
) => {
  switch (action) {
    case 'running':
      return {
        icon: RunningIcon,
        color: theme.palette.info.main || '#2196f3',
        backgroundColor: alpha(theme.palette.info.main || '#2196f3', 0.1),
        chipColor: 'info' as const,
        chipLabel: 'Đang chạy',
      };
    case 'done':
      return {
        icon: DoneIcon,
        color: theme.palette.success.main || '#4caf50',
        backgroundColor: alpha(theme.palette.success.main || '#4caf50', 0.1),
        chipColor: 'success' as const,
        chipLabel: 'Hoàn thành',
      };
    default:
      return {
        icon: DefaultIcon,
        color: theme.palette.grey[600] || '#757575',
        backgroundColor: alpha(theme.palette.grey[500] || '#9e9e9e', 0.1),
        chipColor: 'default' as const,
        chipLabel: 'Thông tin',
      };
  }
};

/**
 * Campaign progress logs display component
 */
const CampaignProgressLogs: React.FC<CampaignProgressLogsProps> = ({
  logs,
  currentCampaignId,
  onCampaignComplete,
}) => {
  console.log('CampaignProgressLogs: logs changed:', logs);
  console.log(
    'CampaignProgressLogs: currentCampaignId changed:',
    currentCampaignId,
  );
  const theme = useTheme();
  const scrollContainerRef = useRef<HTMLDivElement | null>(null);
  const [showNewLogsIndicator, setShowNewLogsIndicator] = useState(false);
  const [lastLogCount, setLastLogCount] = useState(0);

  // Filter logs to show only those for the current campaign
  const filteredLogs = logs.filter(
    (log) => log.campaignId == currentCampaignId,
  );

  console.log('CampaignProgressLogs: filteredLogs changed:', filteredLogs);

  // Simple completion notification state
  const [showCompletionNotification, setShowCompletionNotification] =
    useState(false);
  const [hasShownNotification, setHasShownNotification] = useState(false);

  // Simple completion detection - show notification when campaign completes
  useEffect(() => {
    const hasCompletionLog = filteredLogs.some((log) => log.action === 'done');

    if (hasCompletionLog && !hasShownNotification) {
      console.log(
        `Campaign ${currentCampaignId} completed, showing notification`,
      );
      setShowCompletionNotification(true);
      setHasShownNotification(true);
    }
  }, [filteredLogs, currentCampaignId, hasShownNotification]);

  // Reset notification state when campaign ID changes
  useEffect(() => {
    setHasShownNotification(false);
    setShowCompletionNotification(false);
  }, [currentCampaignId]);

  // Handle user's choice to refresh data
  const handleRefreshConfirm = () => {
    console.log(`User confirmed refresh for campaign ${currentCampaignId}`);
    setShowCompletionNotification(false);
    if (onCampaignComplete) {
      onCampaignComplete();
    }
  };

  // Handle user's choice to dismiss notification
  const handleRefreshDismiss = () => {
    console.log(
      `User dismissed refresh notification for campaign ${currentCampaignId}`,
    );
    setShowCompletionNotification(false);
  };

  // Conditional auto-scroll: only scroll when user is near bottom
  useEffect(() => {
    if (!scrollContainerRef.current) return;

    const scrollContainer = scrollContainerRef.current;
    const { scrollTop, scrollHeight, clientHeight } = scrollContainer;

    // Check if user is near the bottom (within 100px threshold)
    const isNearBottom = scrollTop + clientHeight >= scrollHeight - 100;

    // If new logs were added (use filteredLogs length instead of all logs)
    if (filteredLogs.length > lastLogCount) {
      if (isNearBottom) {
        // User is at bottom, scroll to bottom within the container only
        // Use scrollTo instead of scrollIntoView to prevent page-level scrolling
        scrollContainer.scrollTo({
          top: scrollContainer.scrollHeight,
          behavior: 'smooth',
        });
        setShowNewLogsIndicator(false);
      } else {
        // User has scrolled up, show indicator for new logs
        setShowNewLogsIndicator(true);
      }
      setLastLogCount(filteredLogs.length);
    }
  }, [filteredLogs.length, lastLogCount]); // Use filteredLogs.length instead of entire array

  // Handle manual scroll to bottom when clicking the indicator - memoized
  const scrollToBottom = useCallback(() => {
    if (scrollContainerRef.current) {
      // Use scrollTo instead of scrollIntoView to prevent page-level scrolling
      scrollContainerRef.current.scrollTo({
        top: scrollContainerRef.current.scrollHeight,
        behavior: 'smooth',
      });
      setShowNewLogsIndicator(false);
    }
  }, []);

  // Hide indicator when user manually scrolls to bottom - memoized
  const handleScroll = useCallback(() => {
    if (!scrollContainerRef.current) return;

    const scrollContainer = scrollContainerRef.current;
    const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
    const isNearBottom = scrollTop + clientHeight >= scrollHeight - 100;

    if (isNearBottom) {
      setShowNewLogsIndicator(false);
    }
  }, []);

  return (
    <Card
      elevation={1}
      sx={{
        borderRadius: 3,
        overflow: 'hidden',
        position: 'sticky',
        top: 24,
        maxHeight: 'calc(100vh - 48px)',
        '& > *': {
          position: 'relative', // Ensure proper positioning context for floating button
        },
      }}
    >
      <Box
        sx={{
          p: 3,
          background: `linear-gradient(135deg, ${alpha(theme.palette.success.main || '#4caf50', 0.1)} 0%, ${alpha(theme.palette.success.dark || '#388e3c', 0.05)} 100%)`,
          borderBottom: `1px solid ${theme.palette.divider}`,
        }}
      >
        <Stack direction="row" spacing={2} alignItems="center">
          <Avatar
            sx={{
              width: 48,
              height: 48,
              backgroundColor: theme.palette.success.main || '#4caf50',
            }}
          >
            <InfoIcon />
          </Avatar>
          <Box sx={{ flex: 1 }}>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
              Log thông tin
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Theo dõi tiến trình chiến dịch
            </Typography>
          </Box>
        </Stack>
      </Box>

      <CardContent
        sx={{ p: 0, height: 'calc(100vh - 280px)', overflow: 'hidden' }}
      >
        <Box
          ref={scrollContainerRef}
          onScroll={handleScroll}
          sx={{
            height: '100%',
            overflowY: 'auto',
            '&::-webkit-scrollbar': {
              width: 6,
            },
            '&::-webkit-scrollbar-track': {
              backgroundColor: alpha(theme.palette.grey[300] || '#e0e0e0', 0.3),
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: alpha(theme.palette.grey[500] || '#9e9e9e', 0.5),
              borderRadius: 3,
              '&:hover': {
                backgroundColor: alpha(
                  theme.palette.grey[600] || '#757575',
                  0.7,
                ),
              },
            },
          }}
        >
          {filteredLogs.length > 0 ? (
            <List sx={{ p: 0 }}>
              {filteredLogs.map((log, index) => {
                const actionConfig = getActionConfig(theme, log.action);
                const IconComponent = actionConfig.icon;

                return (
                  <ListItem
                    key={index}
                    sx={{
                      borderBottom:
                        index < filteredLogs.length - 1
                          ? `1px solid ${theme.palette.divider}`
                          : 'none',
                      py: 0.5,
                      px: 2,
                      '&:hover': {
                        backgroundColor: alpha(actionConfig.color, 0.04),
                      },
                    }}
                  >
                    <ListItemAvatar>
                      <Avatar
                        sx={{
                          width: 30,
                          height: 30,
                          backgroundColor: actionConfig.backgroundColor,
                          color: actionConfig.color,
                          fontSize: '1rem',
                        }}
                      >
                        <IconComponent sx={{ fontSize: 20 }} />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Stack
                          direction="row"
                          spacing={1}
                          alignItems="center"
                          sx={{ mb: 0.5 }}
                        >
                          <Typography
                            variant="body2"
                            sx={{
                              fontWeight: 500,
                              lineHeight: 1.4,
                              wordBreak: 'break-word',
                              flex: 1,
                            }}
                          >
                            {log.message}
                          </Typography>
                          <Chip
                            label={actionConfig.chipLabel}
                            color={actionConfig.chipColor}
                            size="small"
                            variant="outlined"
                            sx={{
                              height: 20,
                              fontSize: '0.65rem',
                              fontWeight: 600,
                            }}
                          />
                        </Stack>
                      }
                      secondary={
                        <Typography
                          variant="caption"
                          color="text.secondary"
                          sx={{ mt: 0.5, display: 'block' }}
                        >
                          {new Date().toLocaleTimeString('vi-VN')}
                        </Typography>
                      }
                    />
                  </ListItem>
                );
              })}
            </List>
          ) : (
            <Box
              sx={{
                p: 4,
                textAlign: 'center',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <InfoIcon
                sx={{
                  fontSize: 48,
                  color: alpha(theme.palette.grey[400] || '#bdbdbd', 0.5),
                  mb: 2,
                }}
              />
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                Chưa có log nào
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Logs sẽ xuất hiện khi chiến dịch bắt đầu chạy
              </Typography>
            </Box>
          )}
        </Box>
      </CardContent>

      {/* Floating indicator for new logs when user has scrolled up */}
      <Fade in={showNewLogsIndicator}>
        <Fab
          size="small"
          color="primary"
          onClick={scrollToBottom}
          sx={{
            position: 'absolute',
            bottom: 16,
            right: 16,
            zIndex: 1000,
            boxShadow: theme.shadows[6],
            '&:hover': {
              transform: 'scale(1.1)',
            },
            transition: 'transform 0.2s ease-in-out',
          }}
        >
          <ArrowDownIcon />
        </Fab>
      </Fade>

      {/* Campaign completion notification */}
      <Snackbar
        open={showCompletionNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        sx={{
          position: 'fixed',
          bottom: 24,
          right: 24,
          zIndex: 2000,
        }}
      >
        <Alert
          severity="success"
          variant="filled"
          sx={{
            minWidth: 350,
          }}
          action={
            <Stack direction="row" spacing={1}>
              <Button
                size="small"
                variant="outlined"
                onClick={handleRefreshConfirm}
                sx={{
                  borderColor: 'rgba(255, 255, 255, 0.5)',
                  color: 'white',
                  '&:hover': {
                    borderColor: 'white',
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  },
                }}
              >
                Cập nhật
              </Button>
              <Button
                size="small"
                onClick={handleRefreshDismiss}
                sx={{
                  color: 'rgba(255, 255, 255, 0.8)',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  },
                }}
              >
                Bỏ qua
              </Button>
            </Stack>
          }
        >
          <Typography variant="body2" sx={{ fontWeight: 600, mb: 0.5 }}>
            Chiến dịch đã được hoàn thành
          </Typography>
          <Typography variant="caption" sx={{ opacity: 0.9 }}>
            Cập nhật dữ liệu để xem kết quả mới nhất
          </Typography>
        </Alert>
      </Snackbar>
    </Card>
  );
};

export default CampaignProgressLogs;
