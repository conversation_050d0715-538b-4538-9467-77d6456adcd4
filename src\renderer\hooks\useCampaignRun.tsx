import { useEffect, useState } from 'react';
import { Campaign } from '../../interfaces/Campaigns';
import { IFBUserResponse } from '../../interfaces/IFacebookUser';

/**
 * Fetch campaigns
 */
async function fetchActiveUsers() {
  const response = await window.account.getUserByStatus('active');
  const users = Array.isArray(response) ? response : [];
  return users;
}

export default function useCampaignRun() {
  const [availableUsers, setAvailableUsers] = useState<IFBUserResponse[]>([]);
  const [campaignToRun, setCampaignToRun] = useState<Campaign | null>(null);

  useEffect(() => {
    const loadActiveUsers = async () => {
      try {
        const response = await fetchActiveUsers();
        setAvailableUsers(response);
      } catch (error) {
        console.error('Failed to fetch active users:', error);
      }
    };

    loadActiveUsers();
  }, []);

  return {
    availableUsers,
    setAvailableUsers,
    campaignToRun,
    setCampaignToRun,
  };
}
