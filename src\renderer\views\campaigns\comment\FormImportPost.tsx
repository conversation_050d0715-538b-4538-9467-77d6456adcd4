import { ChangeEvent } from "react";
import { CampaignConfigProps, InputConfig } from "../CampaignConfig";
import InputCheckbox from "../../../components/form/InputCheckbox";
import InputFileUpload from "../../../components/buttons/ButtonUploadFile";
import FormWrapper from "../common/FormWrapper";

export default function FormImportPost({ config, onChange }: CampaignConfigProps) {
  
  const inputs: InputConfig[] = [
    { 
      name: 'postIds', 
      label: 'UID bài viết (phân tách bằng dấu phẩy)', 
      type: 'textarea',
      value: config.postIds,
      multiline: true, 
      rows: 4 
    },
  ];

  const handleUpload = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      file.text()
        .then((text) => onChange('postIds', text))
        .catch(() => {});
    }
  }

  return (
    <FormWrapper title="Danh sách bài viết" description="Nhập danh sách bài viết cho chiến dịch">
      <InputFileUpload
        title="Tải tệp danh sách UID"
        accept=".txt"
        onChange={handleUpload}
        />
      
      {inputs.map((input) => (
        <InputCheckbox
          label={input.label}
          name={input.name}
          checked={!!config[input.name]}
          onChange={(e: ChangeEvent<HTMLInputElement>) => onChange(input.name, e.target.value, input.type)}
          />
        ))}
    </FormWrapper>
  );
}