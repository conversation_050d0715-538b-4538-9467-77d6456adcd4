import { Page } from 'puppeteer-core';
import { IFBUser, IFBUserRequest, IFBUserResponse } from './IFacebookUser';

export interface IAccountFBController {
  findByUserId(
    userId: string,
  ): { success: boolean; data? : IFBUser };
  getUser(id: string|number): { success: boolean; data? : IFBUserResponse }
  createUser({
    username,
    password,
    profileId,
    categoryId,
    userId,
    status
  }: IFBUserRequest): { success: boolean; error?: string; data? : IFBUserResponse };
  getAllUsers(search?: string): IFBUser[];
  deleteUser(id: string): { success: boolean };
  updateUser(
   data : IFBUser
  ): { success: boolean; error?: string; data? : IFBUserResponse};
  launchProfilePage(id: string|number): Promise<{ success: boolean; page? : Page }>
  getUserByStatus(Status: string): Promise<IFBUserResponse[]>;
  bulkDeleteUsers(ids: string[]): boolean;
}
