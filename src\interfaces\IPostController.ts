import { Post } from './Post';

export interface IPostController {
  createPost(post: Post): Promise<{ success: boolean; message: string }>;
  getAllPosts(): Promise<Post[]>;
  deletePost(id: number): Promise<{ success: boolean; message: string }>;
  updatePost(
    id: number,
    post: Post,
  ): Promise<{ success: boolean; message: string }>;
  getPostById(id: number): Promise<Post | undefined>;
}
