import { useState } from 'react';
import FormControl from "@mui/material/FormControl";
import InputLabel from "@mui/material/InputLabel";
import OutlinedInput from "@mui/material/OutlinedInput";
import {FormHelperText} from "@mui/material";
import { useTheme } from '@mui/material/styles';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import Visibility from '@mui/icons-material/Visibility';
import { InputTextProps } from './formType';


export const usePasswordToggle = () => {
  const [showPassword, setShowPassword] = useState(false);

  const handleShowPassword = () => {
    setShowPassword((prev) => !prev);
  };

  return { showPassword, handleShowPassword };
};

function InputPassword ({
  id,
  name,
  label,
  value,
  errorMessage,
  ...rest
}: InputTextProps) {
  
  const theme = useTheme();
  const {showPassword, handleShowPassword} = usePasswordToggle();
  
  return (
    <FormControl fullWidth sx={{ ...theme.typography.customInput }}>
      <InputLabel htmlFor={id}>{label}</InputLabel>
      <OutlinedInput
        id={id}
        name={name}
        type={showPassword ? 'text' : 'password'}
        value={value}
        label={label}
        {...rest}
        endAdornment={
          <InputAdornment position="end">
            <IconButton
              aria-label="toggle password visibility"
              onClick={handleShowPassword}
              edge="end"
              size="large"
            >
              {showPassword ? <Visibility /> : <VisibilityOff />}
            </IconButton>
          </InputAdornment>
        }
      />
      {errorMessage && <FormHelperText error>
          {errorMessage}
        </FormHelperText>}
    </FormControl>
  );
};

InputPassword.defaultProps = {
  errors: undefined,
};

export default InputPassword;