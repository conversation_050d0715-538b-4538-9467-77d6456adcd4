/* eslint-disable react/require-default-props */
import Chip from '@mui/material/Chip';

interface LabelProps {
  children: string;
  color?: 'success' | 'error' | 'warning' | 'info' | 'default';
  variant?: 'outlined';
}

function Label({ children, color, variant }: LabelProps) {
  return (
    <Chip
      label={children}
      color={color}
      size="small"
      variant={variant}
      sx={{ fontWeight: 'medium' }}
    />
  );
}

export default Label;