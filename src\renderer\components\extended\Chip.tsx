/* eslint-disable react/require-default-props */
import { alpha, useTheme } from '@mui/material/styles';
import MuiChip from '@mui/material/Chip';
import type { ChipProps } from '@mui/material';

interface Props extends Omit<ChipProps, 'color' | 'variant'> {
  sx: any,
  chipcolor: string,
  variant?: string,
  disabled: boolean
};

function Chip({ chipcolor, disabled, sx={}, variant, ...others }: Props) {
  const theme = useTheme();

  let defaultSX = {
    color: 'primary.main',
    bgcolor: 'primary.light',
    ':hover': {
      color: 'primary.light',
      bgcolor: 'primary.dark'
    },
    cursor: 'pointer',
  };

  let outlineSX = {
    color: 'primary.main',
    bgcolor: 'transparent',
    border: '1px solid',
    borderColor: 'primary.main',
    ':hover': {
      color: 'primary.light',
      bgcolor: 'primary.dark'
    },
    cursor: 'pointer'
  };

  switch (chipcolor) {
    case 'secondary':
      if (variant === 'outlined') {
        outlineSX = {
          color: 'secondary.main',
          bgcolor: 'transparent',
          border: '1px solid',
          borderColor: 'secondary.main',
          ':hover': {
            color: 'secondary.main',
            bgcolor: 'secondary.light'
          },
          cursor: 'pointer'
        };
      } else {
        defaultSX = {
          color: 'secondary.main',
          bgcolor: 'secondary.light',
          ':hover': {
            color: 'secondary.light',
            bgcolor: 'secondary.main'
          },
          cursor: 'pointer'
        };
      }
      break;
    case 'success':
      if (variant === 'outlined') {
        outlineSX = {
          color: 'success.dark',
          bgcolor: 'transparent',
          border: '1px solid',
          borderColor: 'success.dark',
          ':hover': {
            color: 'success.dark',
            bgcolor: alpha(theme.palette.success.light, 0.6)
          },
          cursor: 'pointer'
        }
      } else {
        defaultSX = {
          color: 'success.dark',
          bgcolor: alpha(theme.palette.success.light, 0.6),
          ':hover': {
            color: 'success.light',
            bgcolor: 'success.dark'
          },
          cursor: 'pointer'
        };
      }
      break;
    case 'error':
      if (variant === 'outlined') {
        outlineSX = {
          color: 'error.main',
          bgcolor: 'transparent',
          border: '1px solid',
          borderColor: 'error.main',
          ':hover': {
            color: 'error.dark',
            bgcolor: 'error.light'
          },
          cursor: 'pointer'
        };
      } else {
        defaultSX = {
          color: 'error.dark',
          bgcolor: alpha(theme.palette.error.light, 0.6),
          ':hover': {
            color: 'error.light',
            bgcolor: 'error.dark'
          },
          cursor: 'pointer'
        };
      }
      break;
    case 'orange':
      if (variant === 'outlined') {
        outlineSX = {
          color: 'orange.dark',
          bgcolor: 'transparent',
          border: '1px solid',
          borderColor: 'orange.main',
          ':hover': {
            color: 'orange.dark',
            bgcolor: 'orange.light'
          },
          cursor: 'pointer'
        };
      } else {
        defaultSX = {
          color: 'orange.dark',
          bgcolor: 'orange.light',
          ':hover': {
            color: 'orange.light',
            bgcolor: 'orange.dark'
          },
          cursor: 'pointer'
        }
      }
      break;
    case 'warning':
      if (variant === 'outlined') {
        outlineSX = {
          color: 'warning.dark',
          bgcolor: 'transparent',
          border: '1px solid',
          borderColor: 'warning.dark',
          ':hover': {
            color: 'warning.dark',
            bgcolor: 'warning.light'
          },
          cursor: 'pointer'
        }
      } else {
        defaultSX = {
          color: 'warning.dark',
          bgcolor: 'warning.light',
          ':hover': {
            color: 'warning.light',
            bgcolor: 'warning.dark'
          },
          cursor: 'pointer'
        };
      }
      break;
    default:
  }

  if (disabled) {
    if (variant === 'outlined') {
      outlineSX = {
        color: 'grey.500',
        bgcolor: 'transparent',
        border: '1px solid',
        borderColor: 'grey.500',
        cursor: 'not-allowed',
        ':hover': {
          color: 'grey.500',
          bgcolor: 'transparent'
        }
      };
    } else {
      defaultSX = {
        color: 'grey.500',
        bgcolor: 'grey.50',
        cursor: 'not-allowed',
        ':hover': {
          color: 'grey.500',
          bgcolor: 'grey.50'
        }
      };
    }
  }

  let SX = defaultSX;
  if (variant === 'outlined') {
    SX = outlineSX;
  }
  SX = {...SX, ...sx};
  return <MuiChip {...others} sx={SX} />;
}



export default Chip;
