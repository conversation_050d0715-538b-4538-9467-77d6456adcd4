import { Database } from 'better-sqlite3';
import { Category } from '../interfaces/Categorys';
import DatabaseManager from '../db/sqlite';
import log from '../utils/logs';

export default class CategoryServices {
  private db: Database;

  public static RESOURCE_TYPE_USER = 'user';

  public static RESOURCE_TYPE_CAMPAIGN_POST = 'campaign_post';

  public static RESOURCE_TYPE_CAMPAIGN_COMMENT = 'campaign_comment';

  public static RESOURCE_TYPE_CONTENT = 'content';

  constructor() {
    this.db = DatabaseManager.getInstance().getDb();

    const categoryCount = this.db
      .prepare('SELECT COUNT(*) as count FROM categories')
      .get() as { count: number };

    // Insert default categories if the table is empty
    if (categoryCount.count === 0) {
      this.db.prepare(
        `INSERT INTO categories (category_name, category_description, category_resourceType) VALUES 
        ('Mặc định', '<PERSON>h mục tài khoản mặc định', ?), 
        ('Mặc định', '<PERSON><PERSON> mục chiến dịch đăng bài mặc định', ?), 
        ('Mặc định', '<PERSON><PERSON> mục nội dung mặc định', ?) `
      ).run(
        CategoryServices.RESOURCE_TYPE_USER,
        CategoryServices.RESOURCE_TYPE_CAMPAIGN_POST,
        CategoryServices.RESOURCE_TYPE_CONTENT
      );
    }
  }

  getCategory(id: string|number): Category | null {
    try {
      return this.db
      .prepare(`SELECT * FROM categories WHERE category_id = ?`)
      .get(id) as Category;
    } catch (error) {
      return null;
    }
  }

  createCategory(name: string, description: string, resourceType: string): number | null {
    try {
      const insertStmt = this.db.prepare(`INSERT INTO categories (category_name, category_description, category_resourceType) VALUES (?, ?, ?)`);

      const result = insertStmt.run(name, description, resourceType);
      return result.lastInsertRowid as number;
      
    } catch (error) {
      log.error("An error occurred when create user", error);
      return null;
    }
  }

  getAllCategorys(resourceType?: string): Category[] {
    try {
      let query = 'SELECT * FROM Categories';
      const params: (string)[] = [];
      
      if (resourceType) {
        query += ' WHERE category_resourceType = ?';
        params.push(resourceType);
      }

      const stmt = this.db.prepare(query);
      return stmt.all(...params) as Category[];
    } catch (error: any) {
      throw new Error(`Failed to fetch categories: ${error.message}`);
    }
  }

  deleteCategory(id: string|number): boolean {
    try {
      if (Number.isNaN(id)) {
        return false;
      }
      const stmt = this.db.prepare('DELETE FROM Categories WHERE category_id = ?');
      const result = stmt.run(id);
      if (result.changes === 0) {
        log.error(`Category with ID ${id} not found`);
        return false;
      }
      return true;
    } catch (error: any) {
      log.error('Failed to delete category:', {error});
      return false;
    }
  }
}
