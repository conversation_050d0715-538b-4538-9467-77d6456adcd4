/**
 * Environment Configuration
 * Manages environment-specific settings and API endpoints
 */

import { Environment } from './ApiConfig';

export interface EnvironmentConfig {
  name: Environment;
  displayName: string;
  apiHost: string;
  crmApiHost: string;
  isDevelopment: boolean;
  isProduction: boolean;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  features: {
    enableDebugMode: boolean;
    enableDevTools: boolean;
    enableErrorReporting: boolean;
    enableAnalytics: boolean;
  };
}

const environments: Record<Environment, EnvironmentConfig> = {
  dev: {
    name: 'dev',
    displayName: 'Development',
    apiHost: 'http://************:8899',
    crmApiHost: 'http://localhost:3000',
    isDevelopment: true,
    isProduction: false,
    logLevel: 'debug',
    features: {
      enableDebugMode: true,
      enableDevTools: true,
      enableErrorReporting: false,
      enableAnalytics: false,
    },
  },
  test: {
    name: 'test',
    displayName: 'Testing',
    apiHost: 'https://api-dev.phanmemtop.vn',
    crmApiHost: 'https://crm-dev.phanmemtop.vn',
    isDevelopment: false,
    isProduction: false,
    logLevel: 'info',
    features: {
      enableDebugMode: true,
      enableDevTools: true,
      enableErrorReporting: true,
      enableAnalytics: false,
    },
  },
  prod: {
    name: 'prod',
    displayName: 'Production',
    apiHost: 'https://api.phanmemtop.vn',
    crmApiHost: 'https://crm.phanmemtop.vn',
    isDevelopment: false,
    isProduction: true,
    logLevel: 'warn',
    features: {
      enableDebugMode: false,
      enableDevTools: false,
      enableErrorReporting: true,
      enableAnalytics: true,
    },
  },
};

export class EnvironmentManager {
  private static _currentEnvironment: Environment = 'prod';
  private static _config: EnvironmentConfig = environments.prod;

  /**
   * Get current environment name
   */
  static get currentEnvironment(): Environment {
    return this._currentEnvironment;
  }

  /**
   * Get current environment configuration
   */
  static get config(): EnvironmentConfig {
    return this._config;
  }

  /**
   * Set environment
   */
  static setEnvironment(env: Environment): void {
    if (environments[env]) {
      this._currentEnvironment = env;
      this._config = environments[env];
      console.log(`Environment set to: ${this._config.displayName}`);
    } else {
      console.warn(`Invalid environment: ${env}. Using production.`);
      this._currentEnvironment = 'prod';
      this._config = environments.prod;
    }
  }

  /**
   * Initialize environment from various sources
   */
  static initialize(): void {
    let env: Environment = 'prod';

    // Try to get environment from various sources
    // 1. Process environment variable
    if (process.env.NODE_ENV === 'development') {
      env = 'dev';
    } else if (process.env.REACT_APP_ENV) {
      env = process.env.REACT_APP_ENV as Environment;
    }

    // 2. Local storage (for runtime switching)
    if (typeof window !== 'undefined') {
      const storedEnv = localStorage.getItem('app_environment') as Environment;
      if (storedEnv && environments[storedEnv]) {
        env = storedEnv;
      }
    }

    this.setEnvironment(env);
  }

  /**
   * Switch environment at runtime (for development/testing)
   */
  static switchEnvironment(env: Environment): void {
    this.setEnvironment(env);
    
    // Store in localStorage for persistence
    if (typeof window !== 'undefined') {
      localStorage.setItem('app_environment', env);
    }
  }

  /**
   * Get all available environments
   */
  static getAvailableEnvironments(): EnvironmentConfig[] {
    return Object.values(environments);
  }

  /**
   * Check if feature is enabled
   */
  static isFeatureEnabled(feature: keyof EnvironmentConfig['features']): boolean {
    return this._config.features[feature];
  }

  /**
   * Get API endpoint URL
   */
  static getApiUrl(endpoint: string): string {
    return `${this._config.apiHost}${endpoint}`;
  }

  /**
   * Get CRM API endpoint URL
   */
  static getCrmApiUrl(endpoint: string): string {
    return `${this._config.crmApiHost}${endpoint}`;
  }

  /**
   * Check if current environment is development
   */
  static isDevelopment(): boolean {
    return this._config.isDevelopment;
  }

  /**
   * Check if current environment is production
   */
  static isProduction(): boolean {
    return this._config.isProduction;
  }

  /**
   * Get environment info for debugging
   */
  static getEnvironmentInfo(): {
    environment: Environment;
    config: EnvironmentConfig;
    buildInfo: {
      nodeEnv: string;
      reactAppEnv: string;
      timestamp: string;
    };
  } {
    return {
      environment: this._currentEnvironment,
      config: this._config,
      buildInfo: {
        nodeEnv: process.env.NODE_ENV || 'unknown',
        reactAppEnv: process.env.REACT_APP_ENV || 'unknown',
        timestamp: new Date().toISOString(),
      },
    };
  }
}

// Initialize environment on module load
EnvironmentManager.initialize();

// Export convenience functions
export const getCurrentEnvironment = () => EnvironmentManager.currentEnvironment;
export const getEnvironmentConfig = () => EnvironmentManager.config;
export const setEnvironment = (env: Environment) => EnvironmentManager.setEnvironment(env);
export const switchEnvironment = (env: Environment) => EnvironmentManager.switchEnvironment(env);
export const isFeatureEnabled = (feature: keyof EnvironmentConfig['features']) => 
  EnvironmentManager.isFeatureEnabled(feature);
export const isDevelopment = () => EnvironmentManager.isDevelopment();
export const isProduction = () => EnvironmentManager.isProduction();

export default EnvironmentManager;
