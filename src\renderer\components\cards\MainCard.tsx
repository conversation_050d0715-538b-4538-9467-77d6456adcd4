/* eslint-disable react/require-default-props */
import React from 'react';

import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardHeader from '@mui/material/CardHeader';
import Divider from '@mui/material/Divider';
import Typography from '@mui/material/Typography';

import type { CardProps as MuiCardProps } from '@mui/material/Card';

const headerSX = {
  '& .MuiCardHeader-action': { mr: 0 }
};

interface MainCardProps extends Omit<MuiCardProps, 'children'> {
  border?: boolean;
  boxShadow?: string;
  children: any;
  withContent?: boolean;
  contentClass?: string;
  contentSX?: object;
  darkTitle?: boolean;
  secondary?: string | object;
  shadow?: string | number;
  sx?: object;
  title?: string;
  elevation?: number;
}

const MainCard = React.forwardRef<HTMLDivElement, MainCardProps>(
  (
    {
      border = false,
      boxShadow,
      children,
      withContent = true,
      contentClass = '',
      contentSX = {},
      darkTitle = false,
      shadow,
      sx = {},
      title,
      ...others
    },
    ref
  ) => {
    return (
      <Card
        ref={ref}
        {...others}
        sx={{
          border: border ? '1px solid' : 'none',
          color: 'text.secondary',
          borderColor: 'divider',
          bgcolor: 'primary.card',
          ':hover': {
            boxShadow: boxShadow ? shadow || '0 2px 14px 0 rgb(32 40 45 / 8%)' : 'inherit'
          },
          ...sx
        }}
      >
        {/* card header and action */}
        {!darkTitle && title && <CardHeader sx={headerSX} title={title} />}
        {darkTitle && title && <CardHeader sx={headerSX} title={<Typography variant="h3">{title}</Typography>}  />}

        {title && <Divider />}

        {withContent && (
          <CardContent sx={contentSX} className={contentClass}>
            {children}
          </CardContent>
        )}
        {!withContent && children}
      </Card>
    );
  }
);

export default MainCard;
