import React from 'react';
import {
  Box,
  Paper,
  Stack,
  Skeleton,
  Card,
  Divider,
  Theme,
} from '@mui/material';

interface AccountTableSkeletonProps {
  theme: Theme;
}

/**
 * Account table skeleton component for loading state
 */
const AccountTableSkeleton: React.FC<AccountTableSkeletonProps> = ({
  theme,
}) => {
  return (
    <Box>
      {/* Header Skeleton */}
      <Paper elevation={1} sx={{ p: 3, mb: 3, borderRadius: 3 }}>
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
        >
          <Box>
            <Skeleton variant="text" width={300} height={40} />
            <Skeleton variant="text" width={200} height={24} />
          </Box>
          <Stack direction="row" spacing={2}>
            <Skeleton
              variant="rectangular"
              width={120}
              height={36}
              sx={{ borderRadius: 1 }}
            />
            <Skeleton
              variant="rectangular"
              width={100}
              height={36}
              sx={{ borderRadius: 1 }}
            />
          </Stack>
        </Stack>
      </Paper>

      {/* Search Skeleton */}
      <Paper elevation={1} sx={{ p: 2, mb: 3, borderRadius: 2 }}>
        <Skeleton variant="rectangular" height={56} sx={{ borderRadius: 2 }} />
      </Paper>

      {/* Table Skeleton */}
      <Card elevation={2} sx={{ borderRadius: 3 }}>
        <Box sx={{ p: 2 }}>
          <Skeleton variant="text" width={200} height={32} />
        </Box>
        <Divider />
        {[...Array(5)].map((_, index) => (
          <Box
            key={index}
            sx={{ p: 2, borderBottom: `1px solid ${theme.palette.divider}` }}
          >
            <Stack direction="row" spacing={2} alignItems="center">
              <Skeleton variant="circular" width={24} height={24} />
              <Skeleton variant="text" width={120} />
              <Skeleton
                variant="rectangular"
                width={100}
                height={32}
                sx={{ borderRadius: 1 }}
              />
              <Skeleton variant="text" width={80} />
              <Skeleton variant="text" width={80} />
              <Skeleton variant="text" width={100} />
              <Skeleton
                variant="rectangular"
                width={80}
                height={24}
                sx={{ borderRadius: 1 }}
              />
              <Stack direction="row" spacing={1}>
                <Skeleton variant="circular" width={32} height={32} />
                <Skeleton variant="circular" width={32} height={32} />
                <Skeleton variant="circular" width={32} height={32} />
              </Stack>
            </Stack>
          </Box>
        ))}
      </Card>
    </Box>
  );
};

export default AccountTableSkeleton;
