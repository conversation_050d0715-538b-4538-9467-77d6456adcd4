import { ipcMain, dialog } from 'electron';
import * as fs from 'fs/promises';
import * as path from 'path';

/**
 * Register Electron API handlers for file operations and dialogs
 */
const registerElectronAPIHandlers = (): void => {
  // Dialog API handlers
  ipcMain.handle('dialog:showOpenDialog', async (_event, options) => {
    try {
      const result = await dialog.showOpenDialog(options);
      return result;
    } catch (error) {
      console.error('Error in showOpenDialog:', error);
      return { canceled: true, filePaths: [] };
    }
  });

  ipcMain.handle('dialog:showSaveDialog', async (_event, options) => {
    try {
      const result = await dialog.showSaveDialog(options);
      return result;
    } catch (error) {
      console.error('Error in showSaveDialog:', error);
      return { canceled: true, filePath: '' };
    }
  });

  // File system API handlers
  ipcMain.handle('fs:readFile', async (_event, filePath: string) => {
    try {
      // Validate file path for security
      const resolvedPath = path.resolve(filePath);
      
      // Check if file exists and is readable
      await fs.access(resolvedPath, fs.constants.R_OK);
      
      // Read file as buffer for binary data (images)
      const fileBuffer = await fs.readFile(resolvedPath);
      return fileBuffer;
    } catch (error) {
      console.error(`Error reading file ${filePath}:`, error);
      throw error;
    }
  });

  ipcMain.handle('fs:writeFile', async (_event, filePath: string, data: Buffer) => {
    try {
      const resolvedPath = path.resolve(filePath);
      
      // Ensure directory exists
      const dir = path.dirname(resolvedPath);
      await fs.mkdir(dir, { recursive: true });
      
      // Write file
      await fs.writeFile(resolvedPath, data);
      return { success: true, path: resolvedPath };
    } catch (error) {
      console.error(`Error writing file ${filePath}:`, error);
      throw error;
    }
  });

  ipcMain.handle('fs:exists', async (_event, filePath: string) => {
    try {
      const resolvedPath = path.resolve(filePath);
      await fs.access(resolvedPath);
      return true;
    } catch {
      return false;
    }
  });

  ipcMain.handle('fs:getFileStats', async (_event, filePath: string) => {
    try {
      const resolvedPath = path.resolve(filePath);
      const stats = await fs.stat(resolvedPath);
      return {
        size: stats.size,
        isFile: stats.isFile(),
        isDirectory: stats.isDirectory(),
        mtime: stats.mtime,
        ctime: stats.ctime,
      };
    } catch (error) {
      console.error(`Error getting file stats ${filePath}:`, error);
      throw error;
    }
  });
};

export default registerElectronAPIHandlers;
