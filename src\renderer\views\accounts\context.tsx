import React, {
  createContext,
  useState,
  useContext,
  useMemo,
  useCallback,
  useEffect,
} from 'react';
import { IFBUserResponse } from '../../../interfaces/IFacebookUser';

type AccountProviderProps = {
  children: React.ReactNode;
};

interface AccountContextType {
  accounts: IFBUserResponse[];
  loading: boolean;
  getAllAccounts: (data: IFBUserResponse[]) => void;
  addAccount: (account: IFBUserResponse) => void;
  updateAccount: (id: string, account: IFBUserResponse) => void;
  deleteAccount: (id: string) => void;
}

/**
 * Fetch accounts
 */
async function fetchAccounts() {
  const response = await window.account.getAllUsers();
  return response;
}

const AccountContext = createContext<AccountContextType | undefined>(undefined);

export function AccountProvider({ children }: AccountProviderProps) {
  const [accounts, setAccounts] = useState<IFBUserResponse[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    setLoading(true);
    setTimeout(async () => {
      try {
        const response = await fetchAccounts();
        setAccounts(response);
      } catch (error) {
        console.error('AccountContext: Error fetching accounts:', error);
      } finally {
        setLoading(false);
      }
    }, 500);
  }, []);

  const getAllAccounts = useCallback((data: IFBUserResponse[]) => {
    console.log(`Context: getAllAccounts called with ${data.length} accounts`);
    setLoading(true);
    setTimeout(() => {
      console.log(`Context: Setting ${data.length} accounts in state`);
      setAccounts(data);
      setLoading(false);
    }, 500);
  }, []);

  const addAccount = useCallback((account: IFBUserResponse) => {
    setAccounts((prev) => [...prev, { ...account }]);
  }, []);

  const updateAccount = useCallback(
    (id: string, updatedAccount: IFBUserResponse) => {
      setAccounts((prev) =>
        prev.map((acc) => (acc.id === id ? { ...updatedAccount, id } : acc)),
      );
    },
    [],
  );

  const deleteAccount = useCallback((id: string) => {
    setAccounts((prev) => {
      console.log(
        `Attempting to delete account with id: ${id} (type: ${typeof id})`,
      );
      console.log(
        'Current accounts:',
        prev.map((a) => `${a.id} (type: ${typeof a.id})`),
      );

      // Handle both string and number comparison
      const filtered = prev.filter((acc) => {
        const accId = String(acc.id);
        const targetId = String(id);
        const shouldKeep = accId !== targetId;
        console.log(
          `Account ${acc.id}: comparing "${accId}" !== "${targetId}" = ${shouldKeep}`,
        );
        return shouldKeep;
      });

      console.log(
        `Removed account ${id} from context. Accounts count: ${prev.length} -> ${filtered.length}`,
      );
      return filtered;
    });
  }, []);

  const contextValues = useMemo(
    () => ({
      loading,
      accounts,
      getAllAccounts,
      addAccount,
      updateAccount,
      deleteAccount,
    }),
    [
      loading,
      accounts,
      getAllAccounts,
      addAccount,
      updateAccount,
      deleteAccount,
    ],
  );

  return (
    <AccountContext.Provider value={contextValues}>
      {children}
    </AccountContext.Provider>
  );
}

export const useAccountContext = () => {
  const context = useContext(AccountContext);
  if (!context) {
    throw new Error('useAccountContext must be used within an AccountProvider');
  }
  return context;
};
