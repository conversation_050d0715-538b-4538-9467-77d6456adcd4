{"name": "top-face-post", "description": "A foundation for scalable desktop apps", "keywords": ["electron", "boilerplate", "react", "typescript", "ts", "sass", "webpack", "hot", "reload"], "homepage": "https://phanmemmarketing.vn/", "bugs": {"url": "https://phanmemmarketing.vn/lien-he"}, "repository": {"type": "git", "url": "git+https://github.com/electron-react-boilerplate/electron-react-boilerplate.git"}, "license": "MIT", "author": {"name": "Top Face Teams", "email": "<EMAIL>", "url": "https://electron-react-boilerplate.js.org"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/amilajack"}], "main": "./.erb/dll/main.bundle.dev.js", "scripts": {"build": "concurrently \"npm run build:main\" \"npm run build:renderer\"", "build:dll": "cross-env NODE_ENV=development TS_NODE_TRANSPILE_ONLY=true NODE_OPTIONS=\"-r ts-node/register --no-warnings\" webpack --config ./.erb/configs/webpack.config.renderer.dev.dll.ts", "build:main": "cross-env NODE_ENV=production TS_NODE_TRANSPILE_ONLY=true NODE_OPTIONS=\"-r ts-node/register --no-warnings\" webpack --config ./.erb/configs/webpack.config.main.prod.ts", "build:renderer": "cross-env NODE_ENV=production TS_NODE_TRANSPILE_ONLY=true NODE_OPTIONS=\"-r ts-node/register --no-warnings\" webpack --config ./.erb/configs/webpack.config.renderer.prod.ts", "postinstall": "ts-node .erb/scripts/check-native-dep.js && electron-builder install-app-deps && npm run build:dll", "lint": "cross-env NODE_ENV=development eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "cross-env NODE_ENV=development eslint . --ext .js,.jsx,.ts,.tsx --fix", "package": "ts-node ./.erb/scripts/clean.js dist && npm run build && electron-builder build --publish never && npm run build:dll", "rebuild": "electron-rebuild --parallel --types prod,dev,optional --module-dir release/app", "prestart": "cross-env NODE_ENV=development TS_NODE_TRANSPILE_ONLY=true NODE_OPTIONS=\"-r ts-node/register --no-warnings\" webpack --config ./.erb/configs/webpack.config.main.dev.ts", "start": "ts-node ./.erb/scripts/check-port-in-use.js && npm run prestart && npm run start:renderer", "start:dev": "cross-env REACT_APP_ENV=dev npm start", "start:test": "cross-env REACT_APP_ENV=test npm start", "start:prod": "cross-env REACT_APP_ENV=prod npm start", "start:main": "concurrently -k -P \"cross-env NODE_ENV=development TS_NODE_TRANSPILE_ONLY=true webpack --watch --config ./.erb/configs/webpack.config.main.dev.ts\" \"electronmon . -- {@}\" --", "start:preload": "cross-env NODE_ENV=development TS_NODE_TRANSPILE_ONLY=true NODE_OPTIONS=\"-r ts-node/register --no-warnings\" webpack --config ./.erb/configs/webpack.config.preload.dev.ts", "start:renderer": "cross-env NODE_ENV=development TS_NODE_TRANSPILE_ONLY=true NODE_OPTIONS=\"-r ts-node/register --no-warnings\" webpack serve --config ./.erb/configs/webpack.config.renderer.dev.ts", "test": "jest"}, "browserslist": ["extends browserslist-config-erb"], "prettier": {"singleQuote": true, "overrides": [{"files": [".prettier<PERSON>", ".eslintrc"], "options": {"parser": "json"}}]}, "jest": {"moduleDirectories": ["node_modules", "release/app/node_modules", "src"], "moduleFileExtensions": ["js", "jsx", "ts", "tsx", "json"], "moduleNameMapper": {"\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$": "<rootDir>/.erb/mocks/fileMock.js", "\\.(css|less|sass|scss)$": "identity-obj-proxy"}, "setupFiles": ["./.erb/scripts/check-build-exists.ts"], "testEnvironment": "jsdom", "testEnvironmentOptions": {"url": "http://localhost/"}, "testPathIgnorePatterns": ["release/app/dist", ".erb/dll"], "transform": {"\\.(ts|tsx|js|jsx)$": "ts-jest"}}, "dependencies": {"@electron/notarize": "^3.0.0", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.2.0", "@mui/lab": "^7.0.0-beta.14", "@mui/material": "^7.2.0", "@mui/system": "^7.2.0", "@mui/utils": "^7.2.0", "@reduxjs/toolkit": "^2.8.2", "@tabler/icons-react": "^3.34.0", "electron-debug": "^4.1.0", "electron-log": "^5.3.2", "electron-updater": "^6.3.9", "env-cmd": "^10.1.0", "formik": "^2.4.6", "framer-motion": "^12.23.6", "material-ui-popup-state": "^5.3.6", "node-cron": "^4.2.0", "puppeteer-core": "^24.12.1", "react": "^19.0.0", "react-device-detect": "^2.2.3", "react-dom": "^19.0.0", "react-perfect-scrollbar": "^1.5.8", "react-redux": "^9.2.0", "react-router": "^7.7.0", "react-router-dom": "^7.3.0", "redux": "^5.0.1", "redux-persist": "^6.0.0", "simplebar-react": "^3.3.2", "swr": "^2.3.4", "uuid": "^11.1.0", "web-vitals": "^5.0.3", "xlsx": "^0.18.5", "yup": "^1.6.1"}, "devDependencies": {"@electron/rebuild": "^3.7.1", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.15", "@svgr/webpack": "^8.1.0", "@types/better-sqlite3": "^7.6.13", "@types/node": "22.13.10", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.17", "@types/webpack-bundle-analyzer": "^4.7.0", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "browserslist-config-erb": "^0.0.3", "chalk": "^4.1.2", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "css-minimizer-webpack-plugin": "^7.0.2", "detect-port": "^2.1.0", "electron": "^37.2.6", "electron-builder": "^25.1.8", "electron-extension-installer": "^2.0.0", "electronmon": "^2.0.3", "eslint": "^8.57.1", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-erb": "^4.1.0", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-typescript": "^4.1.1", "eslint-import-resolver-webpack": "^0.13.10", "eslint-plugin-compat": "^6.0.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^4.6.2", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.3", "identity-obj-proxy": "^3.0.0", "mini-css-extract-plugin": "^2.9.2", "prettier": "^3.5.3", "react-refresh": "^0.16.0", "rimraf": "^6.0.1", "sass": "^1.86.0", "sass-loader": "^16.0.5", "style-loader": "^4.0.0", "terser-webpack-plugin": "^5.3.14", "ts-jest": "^29.2.6", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths-webpack-plugin": "^4.2.0", "url-loader": "^4.1.1", "webpack": "^5.98.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.0", "webpack-merge": "^6.0.1"}, "build": {"productName": "Top Face Post", "appId": "org.erb.ElectronReact", "asar": true, "afterSign": ".erb/scripts/notarize.js", "asarUnpack": "**\\*.{node,dll}", "files": ["dist", "node_modules", "package.json"], "mac": {"notarize": false, "target": {"target": "default", "arch": ["arm64", "x64"]}, "type": "distribution", "hardenedRuntime": true, "entitlements": "assets/entitlements.mac.plist", "entitlementsInherit": "assets/entitlements.mac.plist", "gatekeeperAssess": false}, "dmg": {"contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}]}, "win": {"target": ["nsis"]}, "linux": {"target": ["AppImage"], "category": "Development"}, "directories": {"app": "release/app", "buildResources": "assets", "output": "release/build"}, "extraResources": ["./assets/**"], "publish": {"provider": "github", "owner": "electron-react-boilerplate", "repo": "electron-react-boilerplate"}}, "collective": {"url": "https://opencollective.com/electron-react-boilerplate-594"}, "devEngines": {"runtime": {"name": "node", "version": ">=14.x", "onFail": "error"}, "packageManager": {"name": "npm", "version": ">=7.x", "onFail": "error"}}, "electronmon": {"patterns": ["!**/**", "src/main/**", ".erb/dll/**"], "logLevel": "quiet"}}