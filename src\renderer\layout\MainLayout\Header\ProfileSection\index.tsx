import { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

import { useTheme } from '@mui/material/styles';
import {
  Avatar,
  Box,
  ClickAwayListener,
  Divider,
  IconButton,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Paper,
  Popper,
  Typography,
  Fade,
  useMediaQuery,
} from '@mui/material';

import { IconLogout, IconSettings, IconUser } from '@tabler/icons-react';
import User1 from '../../../../assets/images/users/user-round.svg';

// ==============================|| PROFILE MENU ||============================== //

function ProfileSection() {
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const [open, setOpen] = useState(false);
  const anchorRef = useRef<HTMLButtonElement>(null);

  const handleLogout = async () => {
    // Handle logout logic here
    navigate('/login');
  };

  const handleClose = (event: Event | React.SyntheticEvent) => {
    if (
      anchorRef.current &&
      anchorRef.current.contains(event.target as HTMLElement)
    ) {
      return;
    }
    setOpen(false);
  };

  const handleMenuItemClick = (action: () => void) => {
    action();
    setOpen(false);
  };

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };

  const prevOpen = useRef(open);
  useEffect(() => {
    if (prevOpen.current === true && open === false && anchorRef.current) {
      anchorRef.current.focus();
    }
    prevOpen.current = open;
  }, [open]);

  return (
    <>
      <IconButton
        ref={anchorRef}
        onClick={handleToggle}
        size={isMobile ? 'medium' : 'large'}
        aria-controls={open ? 'profile-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        aria-label="user profile menu"
        sx={{
          padding: 1,
          '&:hover': {
            backgroundColor: theme.palette.action.hover,
          },
        }}
      >
        <Avatar
          src={User1}
          sx={{
            width: isMobile ? 32 : 40,
            height: isMobile ? 32 : 40,
            border: `2px solid ${theme.palette.primary.main}`,
            transition: 'all 0.2s ease-in-out',
            '&:hover': {
              transform: 'scale(1.05)',
            },
          }}
        />
      </IconButton>

      <Popper
        placement="bottom-end"
        open={open}
        anchorEl={anchorRef.current}
        transition
        disablePortal
        sx={{ zIndex: theme.zIndex.modal }}
      >
        {({ TransitionProps }) => (
          <Fade in={open} {...TransitionProps} timeout={200}>
            <Paper
              elevation={8}
              sx={{
                mt: 1,
                minWidth: 200,
                maxWidth: 280,
                borderRadius: 2,
                border: `1px solid ${theme.palette.divider}`,
              }}
            >
              <ClickAwayListener onClickAway={handleClose}>
                <Box>
                  {/* User Info Header */}
                  <Box sx={{ p: 2, pb: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Avatar
                        src={User1}
                        sx={{
                          width: 48,
                          height: 48,
                          border: `2px solid ${theme.palette.primary.main}`,
                        }}
                      />
                      <Box>
                        <Typography variant="h6" sx={{ fontWeight: 600 }}>
                          John Doe
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Project Admin
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                  <Divider />

                  {/* Menu Items */}
                  <List sx={{ py: 1 }}>
                    <ListItemButton
                      onClick={() =>
                        handleMenuItemClick(() => navigate('/settings'))
                      }
                      sx={{
                        borderRadius: 1,
                        mx: 1,
                        '&:hover': {
                          backgroundColor: theme.palette.action.hover,
                        },
                      }}
                    >
                      <ListItemIcon sx={{ minWidth: 40 }}>
                        <IconSettings size="1.25rem" />
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Typography variant="body2">
                            Account Settings
                          </Typography>
                        }
                      />
                    </ListItemButton>

                    <ListItemButton
                      onClick={() =>
                        handleMenuItemClick(() => navigate('/profile'))
                      }
                      sx={{
                        borderRadius: 1,
                        mx: 1,
                        '&:hover': {
                          backgroundColor: theme.palette.action.hover,
                        },
                      }}
                    >
                      <ListItemIcon sx={{ minWidth: 40 }}>
                        <IconUser size="1.25rem" />
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Typography variant="body2">User Profile</Typography>
                        }
                      />
                    </ListItemButton>

                    <Divider sx={{ my: 1 }} />

                    <ListItemButton
                      onClick={() => handleMenuItemClick(handleLogout)}
                      sx={{
                        borderRadius: 1,
                        mx: 1,
                        color: theme.palette.error.main,
                        '&:hover': {
                          backgroundColor: theme.palette.error.light + '20',
                        },
                      }}
                    >
                      <ListItemIcon sx={{ minWidth: 40, color: 'inherit' }}>
                        <IconLogout size="1.25rem" />
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Typography variant="body2" color="inherit">
                            Logout
                          </Typography>
                        }
                      />
                    </ListItemButton>
                  </List>
                </Box>
              </ClickAwayListener>
            </Paper>
          </Fade>
        )}
      </Popper>
    </>
  );
}

export default ProfileSection;
