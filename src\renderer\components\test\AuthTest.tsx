/**
 * Simple Authentication Test Component
 * For testing the simplified authentication system
 */

import React from 'react';
import {
  Box,
  Button,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
} from '@mui/material';
import useAuth from '../../hooks/useAuth';
import { ApiConfig } from '../../../config/ApiConfig';
import EnvironmentManager from '../../../config/environment';

const AuthTest: React.FC = () => {
  const { isAuthenticated, user, login, logout, isLoading, error } = useAuth();

  const handleTestLogin = async () => {
    await login({
      email: '<EMAIL>',
      password: 'password123',
    });
  };

  const handleLogout = async () => {
    await logout();
  };

  const handleEnvironmentChange = (event: any) => {
    const newEnv = event.target.value;
    EnvironmentManager.switchEnvironment(newEnv);
    // Update ApiConfig to sync
    ApiConfig.env = newEnv;
    // Reload page to apply changes
    window.location.reload();
  };

  return (
    <Paper sx={{ p: 3, m: 2 }}>
      <Typography variant="h5" gutterBottom>
        Authentication Test
      </Typography>

      <Box sx={{ mb: 2 }}>
        <Typography variant="body1">
          <strong>Status:</strong>{' '}
          {isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
        </Typography>
        <Typography variant="body1">
          <strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}
        </Typography>
        <Typography variant="body1">
          <strong>API Host:</strong> {ApiConfig.apiHost}
        </Typography>
        <Typography variant="body1">
          <strong>Environment:</strong> {ApiConfig.env}
        </Typography>
      </Box>

      <Divider sx={{ my: 2 }} />

      <Box sx={{ mb: 2 }}>
        <Typography variant="h6" gutterBottom>
          Environment Settings
        </Typography>
        <FormControl fullWidth sx={{ mb: 2 }}>
          <InputLabel>Environment</InputLabel>
          <Select
            value={EnvironmentManager.currentEnvironment}
            label="Environment"
            onChange={handleEnvironmentChange}
          >
            <MenuItem value="dev">Development (Local)</MenuItem>
            <MenuItem value="test">Testing (Staging)</MenuItem>
            <MenuItem value="prod">Production</MenuItem>
          </Select>
        </FormControl>
        <Typography variant="body2" color="textSecondary">
          Current API Host: {EnvironmentManager.config.apiHost}
        </Typography>
        <Typography variant="body2" color="textSecondary">
          Current CRM Host: {EnvironmentManager.config.crmApiHost}
        </Typography>
      </Box>

      <Divider sx={{ my: 2 }} />

      {user && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="h6">User Info:</Typography>
          <Typography variant="body2">Email: {user.email}</Typography>
          <Typography variant="body2">Name: {user.full_name}</Typography>
          <Typography variant="body2">Phone: {user.phone}</Typography>
        </Box>
      )}

      {error && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="body1" color="error">
            <strong>Error:</strong> {error}
          </Typography>
        </Box>
      )}

      <Box sx={{ display: 'flex', gap: 2 }}>
        {!isAuthenticated ? (
          <Button
            variant="contained"
            onClick={handleTestLogin}
            disabled={isLoading}
          >
            Test Login
          </Button>
        ) : (
          <Button
            variant="outlined"
            onClick={handleLogout}
            disabled={isLoading}
          >
            Logout
          </Button>
        )}
      </Box>

      <Box sx={{ mt: 2 }}>
        <Typography variant="caption" color="textSecondary">
          This is a test component for the simplified authentication system. It
          will attempt to login with test credentials when you click "Test
          Login".
        </Typography>
      </Box>
    </Paper>
  );
};

export default AuthTest;
