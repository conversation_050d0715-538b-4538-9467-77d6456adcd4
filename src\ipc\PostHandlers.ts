import { ipcMain } from 'electron';
import { IPostController } from '../interfaces/IPostController';
import PostController from '../controllers/PostController';
import PostServices from '../services/PostServices';

const postServices = new PostServices();
const postController: IPostController = new PostController(postServices);

const registerPostHandlers = (): void => {
  ipcMain.handle('post:create', async (_e, post) => {
    return postController.createPost(post);
  });

  ipcMain.handle('post:getAll', async () => {
    return postController.getAllPosts();
  });

  ipcMain.handle('post:getById', async (_e, id: number) => {
    return postController.getPostById(id);
  });

  ipcMain.handle('post:update', async (_e, data) => {
    const { id, post } = data;
    return postController.updatePost(id, post);
  });

  ipcMain.handle('post:delete', async (_e, id: number) => {
    return postController.deletePost(id);
  });
};

export default registerPostHandlers;
