import { useState } from 'react'

export default function useMultiplestepForm(steps: number) {
  const [currentStep, setCurrentStep] = useState(0)

  const nextStep = () => {
    if (currentStep < steps - 1) {
      setCurrentStep((i) => i + 1)
    }
  }

  const previousStep = () => {
    if (currentStep > 0) {
      setCurrentStep((i) => i - 1)
    }
  }

  const goTo = (index: number) => {
    setCurrentStep(index)
  }

  return {
    currentStep,
    setCurrentStep,
    steps,
    isFirstStep: currentStep === 0,
    isLastStep: currentStep === steps - 1,
    goTo,
    nextStep,
    previousStep
  }
}