/**
 * Device Information Utility
 * Provides device and application information for API requests
 */

/**
 * Get machine/device name
 */
export const getMachineName = (): string => {
  try {
    // Try to get hostname from various sources
    if (typeof window !== 'undefined') {
      // Browser environment
      return window.navigator.userAgent || 'Unknown Device';
    }

    // Fallback to a generated name
    return `Device_${Date.now()}`;
  } catch (error) {
    return 'Unknown Device';
  }
};

/**
 * Get application code
 */
export const getAppCode = (): string => {
  // This should be configured based on your application
  // You might want to get this from environment variables or config
  return process.env.REACT_APP_CODE || 'top-face';
};

/**
 * Get device information for authentication
 */
export const getDeviceInfo = () => {
  return {
    app_code: 'top-face',
    machine_name: 'Win11',
  };
};

/**
 * Generate a unique device identifier
 */
export const getDeviceId = (): string => {
  try {
    // Try to get from localStorage first
    let deviceId = localStorage.getItem('device_id');

    if (!deviceId) {
      // Generate a new device ID
      deviceId = `device_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      localStorage.setItem('device_id', deviceId);
    }

    return deviceId;
  } catch (error) {
    // Fallback if localStorage is not available
    return `device_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }
};

export default {
  getMachineName,
  getAppCode,
  getDeviceInfo,
  getDeviceId,
};
