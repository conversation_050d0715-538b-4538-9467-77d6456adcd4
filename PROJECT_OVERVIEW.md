# Top Face Post - Project Overview

## Project Summary

**Top Face Post** is a comprehensive Facebook automation desktop application built with Electron and React. The application provides a suite of tools for managing Facebook marketing campaigns, including automated posting, commenting, messaging, friend requests, and group scanning. It's designed to help users efficiently manage multiple Facebook accounts and execute social media marketing strategies at scale.

The application features a modern Material-UI interface with Vietnamese localization, indicating its target market for Vietnamese social media marketers and businesses.

## Technology Stack

### Frontend Technologies
- **React 19.0.0** - Modern React with latest features
- **TypeScript** - Type-safe JavaScript development
- **Material-UI (MUI) v7.2.0** - Complete UI component library including:
  - `@mui/material` - Core components
  - `@mui/icons-material` - Icon library
  - `@mui/lab` - Experimental components
  - `@emotion/react` & `@emotion/styled` - CSS-in-JS styling
- **Redux Toolkit 2.8.2** - State management
- **React Router 7.7.0** - Client-side routing
- **Formik 2.4.6** - Form handling and validation
- **Yup 1.6.1** - Schema validation
- **Framer Motion 12.23.6** - Animation library
- **SWR 2.3.4** - Data fetching and caching

### Backend/Main Process Technologies
- **Electron 35.0.2** - Cross-platform desktop framework
- **Node.js** - JavaScript runtime
- **Puppeteer Core 24.12.1** - Browser automation for Facebook interactions
- **Better SQLite3** - Embedded database
- **Node-cron 4.2.0** - Task scheduling

### Database Technology
- **SQLite** with **Better SQLite3** driver
- **Database Schema includes:**
  - Users (Facebook accounts)
  - Categories (account organization)
  - Campaigns (marketing campaigns)
  - Posts (content management)
  - Campaign configurations and details
  - Group and user lists for targeting

### Build Tools and Development Dependencies
- **Webpack 5.98.0** - Module bundler with custom configurations
- **TypeScript 5.x** - Type checking and compilation
- **ESLint** - Code linting with Airbnb configuration
- **Prettier** - Code formatting
- **Jest 29.7.0** - Testing framework
- **Sass 1.86.0** - CSS preprocessing
- **Electron Builder 25.1.8** - Application packaging and distribution

## Architecture Overview

### Electron Architecture
The application follows the standard Electron architecture pattern:

**Main Process (`src/main/main.ts`)**
- Manages application lifecycle
- Creates and controls browser windows
- Handles system-level operations
- Registers IPC handlers for communication with renderer
- Manages database connections and services
- Coordinates Facebook automation tasks

**Renderer Process (`src/renderer/`)**
- React-based user interface
- Communicates with main process via IPC
- Manages application state with Redux
- Handles user interactions and form submissions

**Preload Script (`src/main/preload.ts`)**
- Secure bridge between main and renderer processes
- Exposes specific APIs to the renderer via `contextBridge`
- Implements security best practices by limiting renderer access

### Frontend Architecture
The React frontend follows a modular architecture:

```
src/renderer/
├── components/     # Reusable UI components
├── pages/         # Page-level components
├── views/         # Feature-specific view components
├── layout/        # Layout components and navigation
├── store/         # Redux store configuration
├── hooks/         # Custom React hooks
├── themes/        # Material-UI theme configuration
├── routes/        # React Router configuration
└── menu-items/    # Navigation menu structure
```

### Database Integration and Data Flow

**Database Layer (`src/db/sqlite.ts`)**
- Singleton pattern for database management
- Automatic table creation and schema management
- Centralized database access point

**Service Layer (`src/services/`)**
- Business logic implementation
- Database operations abstraction
- Facebook automation services
- Campaign scheduling and management

**Controller Layer (`src/controllers/`)**
- Request handling and validation
- Service coordination
- Response formatting

**Data Flow:**
1. User interaction in React UI
2. IPC call to main process via preload bridge
3. IPC handler routes to appropriate controller
4. Controller delegates to service layer
5. Service performs database operations or Facebook automation
6. Response flows back through the same chain

## IPC Communication Patterns

The application uses Electron's IPC (Inter-Process Communication) system for secure communication between the main and renderer processes.

### Preload Bridge Setup
The `preload.ts` file uses `contextBridge.exposeInMainWorld` to expose specific APIs:

```typescript
// Account management API
contextBridge.exposeInMainWorld('account', {
  findByUserId: async (userId: string) => 
    ipcRenderer.invoke('account:findByUserId', userId),
  createUser: async (data) => 
    ipcRenderer.invoke('account:createUser', data),
  // ... other account methods
});

// Facebook automation API
contextBridge.exposeInMainWorld('facebookWeb', {
  login: async (data) => 
    ipcRenderer.invoke('facebook:login', data),
  searchGroups: async (profileId, value) => 
    ipcRenderer.invoke('facebook:searchGroups', profileId, value),
  // ... other Facebook methods
});
```

### IPC Handler Registration
The main process registers handlers in `src/ipc/ipcHandlers.ts`:

```typescript
export default function registerIPCHandlers(): void {
  categoryHandlers();
  registerAccountHandlers();
  registerFacebookHandlers();
  CampaignHandlers();
}
```

### Communication Pattern
The application follows a consistent pattern:

**Controller → Service → Database**

Example flow for account operations:
1. **IPC Handler** (`src/ipc/accountHandlers.ts`) - Receives IPC calls
2. **Controller** (`src/controllers/AccountFBController.ts`) - Handles business logic
3. **Service** (`src/services/AccountFBServices.ts`) - Performs operations
4. **Database** (`src/db/sqlite.ts`) - Data persistence

### Key IPC Channels

**Account Management:**
- `account:findByUserId` - Find user by ID
- `account:createUser` - Create new Facebook account
- `account:getAllUsers` - Retrieve all accounts
- `account:updateUser` - Update account information
- `account:deleteUser` - Remove account
- `account:launchProfilePage` - Open Facebook profile

**Facebook Automation:**
- `facebook:login` - Authenticate Facebook account
- `facebook:searchGroups` - Search for Facebook groups
- `facebook:start` - Start automation campaign
- `facebook:stopbycampaign` - Stop specific campaign
- `facebook:stopAll` - Stop all running campaigns

**Campaign Management:**
- `Campaigns:createCampaign` - Create new campaign
- `Campaigns:getAllCampaigns` - Retrieve all campaigns
- `Campaigns:getCampaignDetails` - Get campaign details
- `Campaigns:updateCampaign` - Update campaign
- `Campaigns:deleteCampaign` - Delete campaign

**Category Management:**
- `category:getAllCategorys` - Retrieve categories
- `category:createCategory` - Create new category
- `category:deleteCategory` - Delete category

## Key Features

Based on the menu structure and IPC handlers, the application provides:

### Campaign Management
- **Posting Campaigns** (`/campaign/post`) - Automated Facebook post publishing
- **Comment Campaigns** (`/campaign/comment`) - Automated commenting on posts
- **Messaging Campaigns** (`/campaign/message`) - Direct message automation
- **Friend Request Campaigns** (`/campaign/add-friend`) - Automated friend requests
- **Group Scanning** (`/campaign/scan-group`) - Facebook group discovery and analysis
- **Interaction Scanning** (`/campaign/scan-interaction`) - User interaction analysis

### Account Management
- **Multi-Account Support** - Manage multiple Facebook accounts
- **Account Categories** - Organize accounts by purpose or client
- **Profile Management** - Launch and manage Facebook profiles
- **Authentication** - Secure login and session management

### Automation Features
- **Browser Automation** - Puppeteer-based Facebook interaction
- **Scheduled Campaigns** - Cron-based task scheduling
- **Group Search** - Automated Facebook group discovery
- **Content Management** - Post templates and media handling
- **Campaign Analytics** - Track campaign performance and status

### User Interface
- **Dashboard** (`/dashboard`) - Account overview and management
- **Modern UI** - Material-UI components with Vietnamese localization
- **Responsive Design** - Optimized for desktop usage
- **Real-time Updates** - Live campaign status and progress tracking

## Development Setup

### Prerequisites
- **Node.js** >= 14.x
- **npm** >= 7.x

### Installation
```bash
git clone <repository-url>
cd top.facebook.post
npm install
```

### Development Commands
```bash
# Start development server
npm start

# Build for production
npm run build

# Package application
npm run package

# Run tests
npm test

# Lint code
npm run lint

# Fix linting issues
npm run lint:fix
```

### Development Workflow
1. **Main Process Development**: Edit files in `src/main/` and restart with `npm start`
2. **Renderer Development**: Edit files in `src/renderer/` with hot reload
3. **Database Changes**: Modify schema in `src/db/sqlite.ts`
4. **IPC Changes**: Update handlers in `src/ipc/` and preload in `src/main/preload.ts`

### Build Configuration
The application uses custom Webpack configurations in `.erb/configs/` for:
- Main process bundling
- Renderer process bundling with hot reload
- Preload script compilation
- Production optimization

### Packaging
Electron Builder configuration in `package.json` supports:
- **Windows**: NSIS installer
- **macOS**: DMG with notarization support
- **Linux**: AppImage format

The application is designed for professional social media marketing use with robust automation capabilities and a user-friendly interface.
