import { ChangeEvent } from "react";
import { CampaignConfigProps, InputConfig } from "../CampaignConfig";
import InputCheckbox from "../../../components/form/InputCheckbox";
import InputText from "../../../components/form/InputText";
import FormWrapper from "../common/FormWrapper";
import GeneralConfig from "../common/GeneralConfig";


export default function FormCommentConfig({ config, onChange }: CampaignConfigProps) {
  const inputs: InputConfig[] = [
    { 
      name: 'comment_content',
      label: 'Nội dung bình luận',
      type: 'text', 
      value: config.comment_content,
      required: true 
    },
    { 
      name: 'tagFollowers', 
      label: 'Tag @nguoitheodoi', 
      type: 'checkbox',
      value: config.tagFollowers,
    },
    { 
      name: 'tagNeubat', 
      label: 'Tag @neubat', 
      type: 'checkbox',
      value: config.tagNeubat,
    },
  ];

  return (
    <FormWrapper title="<PERSON><PERSON>u hình đăng bình luận" description="Thiết lập cấu hình cho chiến dịch">
      <GeneralConfig config={config} onChange={onChange} />
      {inputs.map((input) => (
        input.type === 'checkbox' ? (
          <InputCheckbox
            label={input.label}
            name={input.name}
            checked={!!config[input.name]}
            onChange={(e: ChangeEvent<HTMLInputElement>) => onChange(input.name, e.target.value, input.type)}
          />
        ) : (
          <InputText
            id={input.name}
            label={input.label}
            name={input.name}
            type={input.type}
            value={config[input.name] || ''}
            required={input.required}
            onChange={(e: ChangeEvent<HTMLInputElement>) => onChange(input.name, e.target.value, input.type)}
          />
        )
      ))}
    </FormWrapper>
  );
}