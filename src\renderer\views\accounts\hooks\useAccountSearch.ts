import { useState, useEffect, useCallback, useMemo } from 'react';
import { IFBUserResponse } from '../../../../interfaces/IFacebookUser';
import { useAccountContext } from '../context';
import { filterAccounts } from '../../../../utils/accountStatus';

interface UseAccountSearchReturn {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  filteredAccounts: IFBUserResponse[];
  handleSearch: (value: string) => Promise<void>;
}

/**
 * Custom hook for account search and filtering functionality
 */
export const useAccountSearch = (): UseAccountSearchReturn => {
  const [searchQuery, setSearchQuery] = useState<string>('');
  const { accounts, getAllAccounts } = useAccountContext();

  // Filter accounts based on search query
  const filteredAccounts = useMemo(() => {
    return filterAccounts(accounts, searchQuery);
  }, [accounts, searchQuery]);

  // Search handler - only fetch if really needed
  const handleSearch = useCallback(async (value: string) => {
    console.log(`Searching accounts with query: ${value}`);
    // Only fetch from server if we need fresh data for search
    // For now, we rely on local filtering which is more responsive
    // const response = await window.account.getAllUsers(value);
    // getAllAccounts(response);
  }, []);

  // Effect for debounced search
  useEffect(() => {
    if (searchQuery.trim().length > 0) {
      const timeoutId = setTimeout(() => handleSearch(searchQuery), 2000);
      return () => clearTimeout(timeoutId);
    }
  }, [handleSearch, searchQuery]);

  return {
    searchQuery,
    setSearchQuery,
    filteredAccounts,
    handleSearch,
  };
};
