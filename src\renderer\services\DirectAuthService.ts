/**
 * Direct Authentication Service (Simplified Approach)
 * Makes API calls directly from the renderer process
 * Alternative to IPC-based authentication
 */

import { ApiConfig } from '../../config/ApiConfig';
import {
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  RegisterResponse,
  LogoutRequest,
  LogoutResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  SyncSessionRequest,
  SyncSessionResponse,
} from '../../interfaces/IAuthentication';

export class DirectAuthService {
  private baseURL: string;

  constructor() {
    this.baseURL = ApiConfig.apiHost;
  }

  /**
   * Make HTTP request with proper headers
   */
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {},
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;

    const defaultHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    // Add auth header if token exists
    if (ApiConfig.bearerToken) {
      defaultHeaders.Authorization = `Bearer ${ApiConfig.bearerToken}`;
    }

    const response = await fetch(url, {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Create FormData for multipart requests
   */
  private createFormData(data: Record<string, string>): FormData {
    const formData = new FormData();
    Object.entries(data).forEach(([key, value]) => {
      if (value) formData.append(key, value);
    });
    return formData;
  }

  /**
   * Create query string from parameters
   */
  private createQueryString(
    params: Record<string, string | undefined>,
  ): string {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, value);
      }
    });
    const queryString = searchParams.toString();
    return queryString ? `?${queryString}` : '';
  }

  /**
   * User login
   */
  async login(request: LoginRequest): Promise<LoginResponse> {
    try {
      // Include app_code and machine_name in the request body
      const loginData = {
        email: request.email,
        password: request.password,
        app_code: request.app_code,
        machine_name: request.machine_name,
      };

      const response = await this.makeRequest<any>(ApiConfig.loginUrl, {
        method: 'POST',
        body: JSON.stringify(loginData),
      });

      if (response.success && response.data) {
        // Store tokens in ApiConfig
        ApiConfig.bearerToken = response.data.token;
        ApiConfig.sessionId = response.data.sessionId;

        // Store in localStorage for persistence
        localStorage.setItem('auth_token', response.data.token);
        localStorage.setItem('auth_session', response.data.sessionId);
        localStorage.setItem('auth_user', JSON.stringify(response.data.user));
      }

      return response;
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Login failed',
      };
    }
  }

  /**
   * User registration with multipart form data
   */
  async register(request: RegisterRequest): Promise<RegisterResponse> {
    try {
      const formData = this.createFormData({
        email: request.email,
        password: request.password,
        password_confirmation: request.password_confirmation,
        phone: request.phone,
        full_name: request.full_name,
        app_code: request.app_code,
        referal_code: request.referal_code,
      });

      const response = await fetch(`${this.baseURL}${ApiConfig.registerUrl}`, {
        method: 'POST',
        body: formData,
        headers: {
          // Don't set Content-Type, let browser set it with boundary
          ...(ApiConfig.bearerToken && {
            Authorization: `Bearer ${ApiConfig.bearerToken}`,
          }),
        },
      });

      const data = await response.json();
      return data;
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Registration failed',
      };
    }
  }

  /**
   * User logout
   */
  async logout(request?: LogoutRequest): Promise<LogoutResponse> {
    try {
      // Create query parameters for GET request
      const queryParams = this.createQueryString({
        session: request?.session || ApiConfig.sessionId,
      });

      const response = await this.makeRequest<any>(
        `${ApiConfig.logoutUrl}${queryParams}`,
        {
          method: 'GET',
        },
      );

      // Clear local storage regardless of response
      this.clearLocalAuth();

      return response;
    } catch (error: any) {
      // Still clear local auth on error
      this.clearLocalAuth();
      return {
        success: true,
        message: 'Logged out locally',
      };
    }
  }

  /**
   * Refresh token
   */
  async refreshToken(
    request: RefreshTokenRequest,
  ): Promise<RefreshTokenResponse> {
    try {
      // Create query parameters for GET request
      const queryParams = this.createQueryString({
        app_code: request.app_code,
        machine_name: request.machine_name,
        session: request.session || ApiConfig.sessionId,
      });

      const response = await this.makeRequest<any>(
        `${ApiConfig.refreshTokenUrl}${queryParams}`,
        {
          method: 'GET',
        },
      );

      if (response.success && response.data) {
        ApiConfig.bearerToken = response.data.token;
        localStorage.setItem('auth_token', response.data.token);
      }

      return response;
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Token refresh failed',
      };
    }
  }

  /**
   * Sync session
   */
  async syncSession(request: SyncSessionRequest): Promise<SyncSessionResponse> {
    try {
      // Create query parameters for GET request
      const queryParams = this.createQueryString({
        app_code: request.app_code,
      });

      const response = await this.makeRequest<any>(
        `${ApiConfig.syncSessionUrl}${queryParams}`,
        {
          method: 'GET',
        },
      );

      if (response.success && response.data) {
        ApiConfig.bearerToken = response.data.token;
        ApiConfig.sessionId = response.data.sessionId;
        localStorage.setItem('auth_token', response.data.token);
        localStorage.setItem('auth_session', response.data.sessionId);
        localStorage.setItem('auth_user', JSON.stringify(response.data.user));
      }

      return response;
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Session sync failed',
      };
    }
  }

  /**
   * Initialize auth from localStorage
   */
  initializeAuth(): { isAuthenticated: boolean; user: any } {
    const token = localStorage.getItem('auth_token');
    const sessionId = localStorage.getItem('auth_session');
    const userStr = localStorage.getItem('auth_user');

    if (token && sessionId) {
      ApiConfig.bearerToken = token;
      ApiConfig.sessionId = sessionId;

      const user = userStr ? JSON.parse(userStr) : null;
      return { isAuthenticated: true, user };
    }

    return { isAuthenticated: false, user: null };
  }

  /**
   * Clear local authentication data
   */
  private clearLocalAuth(): void {
    ApiConfig.clearAuth();
    localStorage.removeItem('auth_token');
    localStorage.removeItem('auth_session');
    localStorage.removeItem('auth_user');
  }
}

export const directAuthService = new DirectAuthService();
export default directAuthService;
