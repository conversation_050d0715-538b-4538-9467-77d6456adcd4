/**
 * Direct Authentication Service (Simplified Approach)
 * Makes API calls directly from the renderer process
 * Alternative to IPC-based authentication
 */

import { ApiConfig } from '../../config/ApiConfig';
import {
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  RegisterResponse,
  LogoutResponse,
  RefreshTokenResponse,
  SyncSessionResponse,
} from '../../interfaces/IAuthentication';

export class DirectAuthService {
  private baseURL: string;

  constructor() {
    this.baseURL = ApiConfig.apiHost;
  }

  /**
   * Make HTTP request with proper headers
   */
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    const defaultHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    // Add auth header if token exists
    if (ApiConfig.bearerToken) {
      defaultHeaders.Authorization = `Bearer ${ApiConfig.bearerToken}`;
    }

    const response = await fetch(url, {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Create FormData for multipart requests
   */
  private createFormData(data: Record<string, string>): FormData {
    const formData = new FormData();
    Object.entries(data).forEach(([key, value]) => {
      if (value) formData.append(key, value);
    });
    return formData;
  }

  /**
   * User login
   */
  async login(request: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await this.makeRequest<any>(ApiConfig.loginUrl, {
        method: 'POST',
        body: JSON.stringify(request),
      });

      if (response.success && response.data) {
        // Store tokens in ApiConfig
        ApiConfig.bearerToken = response.data.token;
        ApiConfig.sessionId = response.data.sessionId;

        // Store in localStorage for persistence
        localStorage.setItem('auth_token', response.data.token);
        localStorage.setItem('auth_session', response.data.sessionId);
        localStorage.setItem('auth_user', JSON.stringify(response.data.user));
      }

      return response;
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Login failed',
      };
    }
  }

  /**
   * User registration with multipart form data
   */
  async register(request: RegisterRequest): Promise<RegisterResponse> {
    try {
      const formData = this.createFormData({
        email: request.email,
        password: request.password,
        password_confirmation: request.password_confirmation,
        phone: request.phone,
        full_name: request.full_name,
        app_code: request.app_code,
        referal_code: request.referal_code,
      });

      const response = await fetch(`${this.baseURL}${ApiConfig.registerUrl}`, {
        method: 'POST',
        body: formData,
        headers: {
          // Don't set Content-Type, let browser set it with boundary
          ...(ApiConfig.bearerToken && {
            Authorization: `Bearer ${ApiConfig.bearerToken}`,
          }),
        },
      });

      const data = await response.json();
      return data;
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Registration failed',
      };
    }
  }

  /**
   * User logout
   */
  async logout(): Promise<LogoutResponse> {
    try {
      const response = await this.makeRequest<any>(ApiConfig.logoutUrl, {
        method: 'POST',
      });

      // Clear local storage regardless of response
      this.clearLocalAuth();

      return response;
    } catch (error: any) {
      // Still clear local auth on error
      this.clearLocalAuth();
      return {
        success: true,
        message: 'Logged out locally',
      };
    }
  }

  /**
   * Refresh token
   */
  async refreshToken(): Promise<RefreshTokenResponse> {
    try {
      const response = await this.makeRequest<any>(ApiConfig.refreshTokenUrl, {
        method: 'POST',
      });

      if (response.success && response.data) {
        ApiConfig.bearerToken = response.data.token;
        localStorage.setItem('auth_token', response.data.token);
      }

      return response;
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Token refresh failed',
      };
    }
  }

  /**
   * Sync session
   */
  async syncSession(sessionId: string): Promise<SyncSessionResponse> {
    try {
      const response = await this.makeRequest<any>(ApiConfig.syncSessionUrl, {
        method: 'POST',
        body: JSON.stringify({ sessionId }),
      });

      if (response.success && response.data) {
        ApiConfig.bearerToken = response.data.token;
        ApiConfig.sessionId = response.data.sessionId;
        localStorage.setItem('auth_token', response.data.token);
        localStorage.setItem('auth_session', response.data.sessionId);
        localStorage.setItem('auth_user', JSON.stringify(response.data.user));
      }

      return response;
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Session sync failed',
      };
    }
  }

  /**
   * Initialize auth from localStorage
   */
  initializeAuth(): { isAuthenticated: boolean; user: any } {
    const token = localStorage.getItem('auth_token');
    const sessionId = localStorage.getItem('auth_session');
    const userStr = localStorage.getItem('auth_user');

    if (token && sessionId) {
      ApiConfig.bearerToken = token;
      ApiConfig.sessionId = sessionId;
      
      const user = userStr ? JSON.parse(userStr) : null;
      return { isAuthenticated: true, user };
    }

    return { isAuthenticated: false, user: null };
  }

  /**
   * Clear local authentication data
   */
  private clearLocalAuth(): void {
    ApiConfig.clearAuth();
    localStorage.removeItem('auth_token');
    localStorage.removeItem('auth_session');
    localStorage.removeItem('auth_user');
  }
}

export const directAuthService = new DirectAuthService();
export default directAuthService;
